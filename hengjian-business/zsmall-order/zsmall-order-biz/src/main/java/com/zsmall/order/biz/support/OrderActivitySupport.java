package com.zsmall.order.biz.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.biz.support.ProductActiveSupper;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityService;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityStockService;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productActivity.ProductActivityExceptionEnum;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.util.RetryUtil;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemProductSku;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderItemProductSkuService;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrdersService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年7月10日  16:45
 * @description: 订单活动支持类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderActivitySupport {

    private final IDistributorProductActivityService distributorProductActivityService;
    private final IDistributorProductActivityStockService distributorProductActivityStockService;
    private final IOrdersService iOrdersService;
    private final ProductActiveSupper productActiveSupper;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;

    /**
     * 获取分销商活动
     * @param tenantId
     * @param productSkuCode
     * @param site
     * @param supportedLogistics
     * @return
     */
    @InMethodLog("获取分销商活动")
    public List<DistributorProductActivity> getDistributorActivity(String tenantId, String productSkuCode,String site,String supportedLogistics) {
        // todo 250711 发货方式需要修改，活动里有两者都支持的发货方式，字段不明
        // 根据分销商租户Id、productSkuCode、活动状态获取分销商活动
        LambdaQueryWrapper<DistributorProductActivity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistributorProductActivity::getDistributorTenantId, tenantId);
        queryWrapper.eq(DistributorProductActivity::getProductSkuCode, productSkuCode);
        queryWrapper.eq(DistributorProductActivity::getSite, site);
        queryWrapper.eq(DistributorProductActivity::getSupportedLogistics, supportedLogistics);
        queryWrapper.eq(DistributorProductActivity::getExceptionCode, ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
        queryWrapper.in(DistributorProductActivity::getActivityState, Arrays.asList(ProductActivityStateEnum.InProgress.name()));
        List<DistributorProductActivity> distributorProductActivityList = distributorProductActivityService.list(queryWrapper);
        if(CollUtil.isEmpty(distributorProductActivityList)){
            log.info("没有获取到分销商活动,tenantId:{},productSkuCode:{}",tenantId,productSkuCode);
            return null;
        }
        return null;
    }


    /**
     * 根据SKU和站点以及发货方式获取有活动库存的活动
     *
     * @param tenantId
     * @param productSkuCode
     * @param site
     * @param supportedLogistics
     * @return
     */
    public DistributorProductActivity getDistributorActivityByStock(String tenantId, String productSkuCode,String site,String supportedLogistics) {
        // 根据分销商租户Id、productSkuCode、活动状态获取分销商活动
        LambdaQueryWrapper<DistributorProductActivity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistributorProductActivity::getDistributorTenantId, tenantId);
        queryWrapper.eq(DistributorProductActivity::getProductSkuCode, productSkuCode);
        queryWrapper.eq(DistributorProductActivity::getSite, site);
        queryWrapper.eq(DistributorProductActivity::getExceptionCode, ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
        queryWrapper.in(DistributorProductActivity::getSupportedLogistics, supportedLogistics, SupportedLogisticsEnum.All.name());
        queryWrapper.in(DistributorProductActivity::getActivityState, Arrays.asList(ProductActivityStateEnum.InProgress.name()));
        List<DistributorProductActivity> distributorProductActivityList = distributorProductActivityService.list(queryWrapper);
        if(CollUtil.isEmpty(distributorProductActivityList)){
            log.info("没有获取到分销商活动,tenantId:{},productSkuCode:{}",tenantId,productSkuCode);
            return null;
        }
        for(DistributorProductActivity distributorProductActivity : distributorProductActivityList){
            List<DistributorProductActivityStock> distributorProductActivityStockList = distributorProductActivityStockService.list(
                new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityId, distributorProductActivity.getId()));
            if(CollUtil.isNotEmpty(distributorProductActivityStockList)){
               for(DistributorProductActivityStock distributorProductActivityStock : distributorProductActivityStockList){
                   if(distributorProductActivityStock.getQuantitySurplus() > 0){
                       return distributorProductActivity;
                   }
               }
            }
        }
        return null;
    }

    /**
     * 获取活动仓库信息,判断活动剩余库存数量，返回第一个锁货剩余数量大于0的仓库信息
     * @param distributorProductActivity
     * @return
     */
    @InMethodLog("获取活动仓库信息")
    public DistributorProductActivityStock getActivityWarehouseInfo(DistributorProductActivity distributorProductActivity) {
        if(null == distributorProductActivity || null == distributorProductActivity.getId()){
            log.info("参数为空，{}",distributorProductActivity);
            return null;
        }
        List<DistributorProductActivityStock> distributorProductActivityStockList = distributorProductActivityStockService.list(
            new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityId, distributorProductActivity.getId()));
        if(CollUtil.isEmpty(distributorProductActivityStockList)){
            log.info("没有获取到活动仓库信息,distributorProductActivityId:{}",distributorProductActivity.getId());
            return null;
        }
        for(DistributorProductActivityStock distributorProductActivityStock : distributorProductActivityStockList){
            if(distributorProductActivityStock.getQuantitySurplus() > 0){
                return distributorProductActivityStock;
            }
        }
        return null;
    }

    /**
     * 获取活动可用库存的仓库信息
     * @param activityCode
     * @param quantity
     * @return
     */
    @InMethodLog("获取活动可用库存的仓库信息")
    public List<DistributorProductActivityStock> getActivityAvailableWarehouseInfo(String activityCode,Integer quantity) {
        if(null == activityCode || null == quantity){
            log.info("活动code为空:{}，数量为空:{}",activityCode,quantity);
            return null;
        }
        DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
            distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode))
        );
        if(null == distributorProductActivity || null == distributorProductActivity.getActivityState()){
            log.info("没有获取到活动信息,activityCode:{},活动状态为null",activityCode);
            return null;
        }
//        if(!ProductActivityStateEnum.InProgress.name().equals(distributorProductActivity.getActivityState())){
//            log.info("活动{} 不在进行中",activityCode);
//            return null;
//        }
        List<DistributorProductActivityStock> distributorProductActivityStockList = TenantHelper.ignore(() ->distributorProductActivityStockService.list(
            new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityCode, activityCode)
                .ge(DistributorProductActivityStock::getQuantitySurplus, quantity)
        ));
        return distributorProductActivityStockList;
    }

    @InMethodLog("获取活动库存的仓库信息")
    public List<DistributorProductActivityStock> getActivityWarehouseInfo(String activityCode,Integer quantity) {
        if(null == activityCode || null == quantity){
            log.info("活动code为空:{}，数量为空:{}",activityCode,quantity);
            return null;
        }
        DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
            distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode))
        );
        if(null == distributorProductActivity || null == distributorProductActivity.getActivityState()){
            log.info("没有获取到活动信息,activityCode:{},活动状态为null",activityCode);
            return null;
        }
//        if(!ProductActivityStateEnum.InProgress.name().equals(distributorProductActivity.getActivityState())){
//            log.info("活动{} 不在进行中",activityCode);
//            return null;
//        }
        List<DistributorProductActivityStock> distributorProductActivityStockList = TenantHelper.ignore(() ->distributorProductActivityStockService.list(
            new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityCode, activityCode)
        ));
        List<DistributorProductActivityStock> distributorProductActivityStockReturnList = new ArrayList<>();
        for(DistributorProductActivityStock distributorProductActivityStock : distributorProductActivityStockList){
            if(distributorProductActivityStock.getQuantitySurplus() >= quantity){
                distributorProductActivityStockReturnList.add(distributorProductActivityStock);
            }
        }
        // 返回的仓库不能为null
        if(CollUtil.isEmpty(distributorProductActivityStockReturnList)){
            distributorProductActivityStockReturnList.addAll(distributorProductActivityStockList);
        }
        return distributorProductActivityStockReturnList;
    }

    /**
     * 订单的物流类型和活动物流类型转换
     * @param logisticsType
     */
    @InMethodLog("订单的物流类型和活动物流类型转换")
    public String logisticsTypeConvert(LogisticsTypeEnum logisticsType){
        if(null == logisticsType){
            log.info("物流类型为空:{}",logisticsType);
            return null;
        }
        switch (logisticsType){
            case PickUp:
                return SupportedLogisticsEnum.PickUpOnly.name();
            case DropShipping:
            case ZSMallDropShipping:
                return SupportedLogisticsEnum.DropShippingOnly.name();
            default:
                return null;
        }
    }

    /**
     * 判断活动订单的活动是否有异常状态及若活动已完成、已过期、已取消，重新匹配新的活动，没有活动则去除订单的活动标识
     * @param ordersList
     * @return  Boolean true:有异常状态 false:没有异常状态
     */
    @InMethodLog("判断活动订单的活动是否有异常状态及若活动已完成、已过期、已取消，重新匹配新的活动，没有活动则去除订单的活动标识")
    public void checkActivityException(List<Orders> ordersList) {
        if (CollUtil.isEmpty(ordersList)){
            return;
        }
        List<Orders> ordersExceptionList = new ArrayList<>();
        for(Orders orders : ordersList){
            String activityCode = orders.getActivityCode();
            if(StringUtils.isEmpty(activityCode)){
                continue;
            }
            DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
                distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode)));
            if(null == distributorProductActivity){
                log.info("分销商活动{} 没查询到", activityCode);
                continue;
            }
            if(null != distributorProductActivity.getActivityState()){
                List<String> activityStateList = Arrays.asList(ProductActivityStateEnum.Completed.getValue(), ProductActivityStateEnum.Expired.getValue(), ProductActivityStateEnum.Canceled.getValue());
                if(activityStateList.contains(distributorProductActivity.getActivityState())){
                    OrderItem orderItem = iOrderItemService.getBaseMapper()
                                                           .selectOne(new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderNo, orders.getOrderNo()));
                    OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getBaseMapper()
                                                                                         .selectOne(new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, orders.getOrderNo()));
                    if(null == orderItem || null == orderItemProductSku){
                        log.info("订单{}或订单商品{}未找到", orders.getOrderNo(), orders.getOrderNo());
                        continue;
                    }
                    // 判断订单是否还有其他匹配的订单
                    DistributorProductActivity distributorActivity = getDistributorActivityByStock(orders.getTenantId(), orderItem.getProductSkuCode(), orders.getCountryCode(), logisticsTypeConvert(orders.getLogisticsType()));
                    if(null == distributorActivity){
                        iOrdersService.update(
                            new Orders(),
                            new LambdaUpdateWrapper<Orders>()
                                .eq(Orders::getOrderNo, orders.getOrderNo())
                                .set(Orders::getActivityCode, null)
                                .set(Orders::getActivityType, null)
                        );
                        iOrderItemService.update(
                            new OrderItem(),
                            new LambdaUpdateWrapper<OrderItem>()
                                .eq(OrderItem::getOrderNo, orders.getOrderNo())
                                .set(OrderItem::getActivityCode, null)
                                .set(OrderItem::getActivityType, null)
                        );
                        iOrderItemProductSkuService.update(
                            new OrderItemProductSku(),
                            new LambdaUpdateWrapper<OrderItemProductSku>()
                                .eq(OrderItemProductSku::getOrderNo, orders.getOrderNo())
                            .set(OrderItemProductSku::getActivityCode, null)
                            .set(OrderItemProductSku::getActivityType, null)
                        );
                        // 将对象中的值也设置为 null
                        if(null != orders.getLogisticsType() && orders.getLogisticsType().equals(LogisticsTypeEnum.DropShipping)){
                            orderItemProductSku.setSpecifyWarehouse(null);
                            orderItemProductSku.setWarehouseSystemCode(null);
                            iOrderItemProductSkuService.update(
                                new OrderItemProductSku(),
                                new LambdaUpdateWrapper<OrderItemProductSku>()
                                    .eq(OrderItemProductSku::getOrderNo, orders.getOrderNo())
                                    .set(OrderItemProductSku::getSpecifyWarehouse, null)
                                    .set(OrderItemProductSku::getWarehouseSystemCode, null)
                            );
                        }
                        orders.setActivityType(null);
                        orders.setActivityCode(null);
                        orderItem.setActivityType(null);
                        orderItem.setActivityCode(null);
                        orderItemProductSku.setActivityType(null);
                        orderItemProductSku.setActivityCode(null);
                    }else {
                        if(null != distributorActivity.getActivityState() && ProductActivityStateEnum.InProgress.name().equals(distributorActivity.getActivityState())){
                            orders.setActivityCode(distributorActivity.getDistributorActivityCode());
                            orders.setActivityType(distributorActivity.getActivityType());
                            orderItem.setActivityCode(distributorActivity.getDistributorActivityCode());
                            orderItem.setActivityType(distributorActivity.getActivityType());
                            orderItemProductSku.setActivityCode(distributorActivity.getDistributorActivityCode());
                            orderItemProductSku.setActivityType(distributorActivity.getActivityType());
                            List<DistributorProductActivityStock> distributorProductActivityStockList = distributorProductActivityStockService.getBaseMapper().selectList(
                                new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityId, distributorActivity.getId()));
                            if(CollUtil.isNotEmpty(distributorProductActivityStockList)){
                                DistributorProductActivityStock distributorProductActivityStock = distributorProductActivityStockList.get(0);
                                orderItemProductSku.setSpecifyWarehouse(distributorProductActivityStock.getWarehouseSystemCode());
                                orderItemProductSku.setWarehouseSystemCode(distributorProductActivityStock.getWarehouseSystemCode());
                            }
                            iOrdersService.updateById(orders);
                            iOrderItemService.updateById(orderItem);
                            iOrderItemProductSkuService.updateById(orderItemProductSku);
                        }
                    }
                }
            }
            if (null != distributorProductActivity.getExceptionCode() && ProductActivityExceptionEnum.NOT_EXCEPTION.getCode() != distributorProductActivity.getExceptionCode()){
                orders.setOrderState(OrderStateType.Failed);
                orders.setExceptionCode(OrderExceptionEnum.activity_exception.getValue());
                // 根据不同的异常，赋值错误信息
                int exceptionCode = distributorProductActivity.getExceptionCode();
                if (exceptionCode == ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode()) {
                    orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_ACTIVITY_EXCEPTION_STOCK_PULL_LOCK_EXCEPTION)
                                                           .toJSON());
                } else if (exceptionCode == ProductActivityExceptionEnum.ERP_LOCK_EXCEPTION.getCode()) {
                    orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_ACTIVITY_EXCEPTION_ERP_LOCK_EXCEPTION)
                                                           .toJSON());
                } else if (exceptionCode == ProductActivityExceptionEnum.ERP_RELEASE_EXCEPTION.getCode()) {
                    orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_ACTIVITY_EXCEPTION_ERP_RELEASE_EXCEPTION)
                                                           .toJSON());
                }
                iOrdersService.updateById(orders);
                ordersExceptionList.add(orders);
            }
        }
        if(CollUtil.isNotEmpty(ordersExceptionList)){
            ordersList.removeIf(order -> ordersExceptionList.stream()
                                                            .anyMatch(ex -> ex.getOrderNo().equals(order.getOrderNo())));
        }
    }

    /**
     * 调用erp释放库存接口
     * @param orderList
     */
    @InMethodLog("erp释放库存接口-批量处理")
    public Boolean releaseInventoryBatch(List<Orders> orderList, List<OrderItemShippingRecord> shippingRecordList){
        for (Orders orders : orderList){
            String activityCode = orders.getActivityCode();
            if(StringUtils.isEmpty(activityCode)){
                return true;
            }
            Integer isRelease = orders.getIsRelease();
            if(null != isRelease && isRelease.equals(OrderIsReleaseEnum.Success.getValue())){
                return true;
            }
            DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
                distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode)));
            if(null == distributorProductActivity){
                return false;
            }
            OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getBaseMapper()
                                                                                 .selectOne(new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, orders.getOrderNo()));
            if(null == orderItemProductSku){
                log.info("订单信息异常,未找到订单商品信息");
                return false;
            }
            String warehouseSystemCode = orderItemProductSku.getWarehouseSystemCode();
            DistributorProductActivityStock distributorProductActivityStock = distributorProductActivityStockService.getBaseMapper().selectOne(
                new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityId, distributorProductActivity.getId())
                                                                         .eq(DistributorProductActivityStock::getWarehouseSystemCode, warehouseSystemCode));
            if(null == distributorProductActivityStock){
                log.info("分销商活动{} 库存不存在", activityCode);
                return true;
            }
            // 调用erp释放库存接口，失败的情况下重试三次，每次间隔3秒
            boolean success = RetryUtil.executeWithRetry(
                () -> productActiveSupper.productLockInventoryReleaseOpenApi(
                    distributorProductActivity.getDistributorActivityCode(),
                    distributorProductActivityStock.getId(),
                    orders.getTotalQuantity()
                ),
                3,
                3000
            );
            if (!success) {
                log.info("订单{}活动{}释放库存失败", orders.getOrderNo(), distributorProductActivity.getDistributorActivityCode());
                if(CollUtil.isNotEmpty(shippingRecordList)){
                    for(OrderItemShippingRecord shippingRecord : shippingRecordList){
                        shippingRecord.setShippingState(ShippingStateEnum.CreateFailed);
                        shippingRecord.setShippingErrorCode(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_EXCEPTION_RELEASE_EXCEPTION).toMessage());
                        shippingRecord.setShippingErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_EXCEPTION_RELEASE_EXCEPTION).toJSON());
                    }
                }
                orders.setFulfillmentProgress(LogisticsProgress.Abnormal);
                orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_EXCEPTION_RELEASE_EXCEPTION)
                                                       .toJSON());
                orders.setExceptionCode(OrderExceptionEnum.abnormal_exception.getValue());
                orders.setIsRelease(OrderIsReleaseEnum.Failed.getValue());
                iOrdersService.updateById(orders);
                return false;
            }else {
                log.info("订单{}活动{}释放库存成功", orders.getOrderNo(), distributorProductActivity.getDistributorActivityCode());
                orders.setIsRelease(OrderIsReleaseEnum.Success.getValue());
                iOrdersService.updateById(orders);
                return true;
            }

        }
        return true;

    }

    /**
     * erp释放库存接口
     * @param orders
     * @return
     */
    @InMethodLog("erp释放库存接口")
    public Boolean releaseInventory(Orders orders, List<OrderItemShippingRecord> shippingRecordList){
        String activityCode = orders.getActivityCode();
        if(StringUtils.isEmpty(activityCode)){
            return true;
        }
        Integer isRelease = orders.getIsRelease();
        if(null != isRelease && isRelease.equals(OrderIsReleaseEnum.Success.getValue())){
            return true;
        }
        DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
            distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode)));
        if(null == distributorProductActivity){
            log.info("分销商活动{} 不存在", activityCode);
            return false;
        }
        OrderItemProductSku orderItemProductSku = TenantHelper.ignore(()->iOrderItemProductSkuService.getBaseMapper()
                                                                                                     .selectOne(new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, orders.getOrderNo())));
        if(null == orderItemProductSku){
            log.info("订单信息异常,未找到订单商品信息");
            return false;
        }
        String warehouseSystemCode = orderItemProductSku.getWarehouseSystemCode();
        DistributorProductActivityStock distributorProductActivityStock = distributorProductActivityStockService.getBaseMapper().selectOne(
            new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityId, distributorProductActivity.getId())
                .eq(DistributorProductActivityStock::getWarehouseSystemCode, warehouseSystemCode));
        if(null == distributorProductActivityStock){
            log.info("分销商活动{} 库存不存在", activityCode);
            return false;
        }
        // 调用erp释放库存接口，失败的情况下重试三次，每次间隔3秒
        boolean success = RetryUtil.executeWithRetry(
            () -> productActiveSupper.productLockInventoryReleaseOpenApi(
                distributorProductActivity.getDistributorActivityCode(),
                distributorProductActivityStock.getId(),
                orders.getTotalQuantity()
            ),
            3,
            3000
        );
        if (!success) {
            log.info("订单{}活动{}释放库存失败", orders.getOrderNo(), distributorProductActivity.getDistributorActivityCode());
            if(CollUtil.isNotEmpty(shippingRecordList)){
                for(OrderItemShippingRecord shippingRecord : shippingRecordList){
                    shippingRecord.setShippingState(ShippingStateEnum.CreateFailed);
                    shippingRecord.setShippingErrorCode(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_EXCEPTION_RELEASE_EXCEPTION).toMessage());
                    shippingRecord.setShippingErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_EXCEPTION_RELEASE_EXCEPTION).toJSON());
                }
            }
            orders.setFulfillmentProgress(LogisticsProgress.Abnormal);
            orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_EXCEPTION_RELEASE_EXCEPTION)
                                                   .toJSON());
            orders.setExceptionCode(OrderExceptionEnum.abnormal_exception.getValue());
            orders.setIsRelease(OrderIsReleaseEnum.Failed.getValue());
            TenantHelper.ignore(()->iOrdersService.updateById(orders));
            return false;
        }else {
            log.info("订单{}活动{}释放库存成功", orders.getOrderNo(), distributorProductActivity.getDistributorActivityCode());
            orders.setIsRelease(OrderIsReleaseEnum.Success.getValue());
            TenantHelper.ignore(()->iOrdersService.updateById(orders));
            return true;
        }
    }


}
