package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.core.utils.trans.TransactionUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.ThirdReceiptDTO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.erp.ErpExceptionEnums;
import com.zsmall.common.enums.priceLog.PriceOperateLog;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import com.zsmall.order.biz.service.OrderTripartiteEntryService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderItemPriceService;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.product.entity.anno.IsDuplication;
import com.zsmall.product.entity.domain.ProductReviewChangeDetail;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.ProductSkuPriceLog;
import com.zsmall.product.entity.domain.bo.siteBo.SitePriceCleanBo;
import com.zsmall.product.entity.domain.vo.ProductReviewRecordVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/21 22:14
 */
@Slf4j
@Manager
public class OrderTripartiteEntryServiceImpl implements OrderTripartiteEntryService {

    @Resource
    private IProductSkuPriceService iProductSkuPriceService;

    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IOrderItemService iOrderItemService;

    @Resource
    private IOrderItemPriceService iOrderItemPriceService;

    @Resource
    private IProductReviewChangeDetailService iProductReviewChangeDetailService;
    @Resource
    private IProductSkuPriceLogService iProductSkuPriceLogService;


    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;

    //    @XxlConf(value = "distribution.tenant.third.flag.erp",defaultValue = "Erp-Test-01")  @Value("")
    @Value("${distribution.tenant.third.flag.erp}")
    private String thirdChannelFlag;

    //    @XxlConf(value = "distribution.tenant.id.erp",defaultValue = "DE9D45Z")   @Value("${xxl.conf.admin.address}")
    @Value("${distribution.tenant.id.erp}")
    private String tenantId;
    @Resource
    private OrdersService ordersService;

    @Resource
    private TransactionUtils transactionUtils;

    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;

    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private ThirdOrderOperationFactory factory;

    @Resource
    private IProductReviewRecordService iProductReviewRecordService;

    @Override
    public R tripartiteBatchEnterForErp(List<OrderReceiveFromThirdDTO> dto) throws InterruptedException {
        R r = R.ok();
        StringBuilder errorOrderNo = new StringBuilder();
        StringBuilder message = new StringBuilder();
        AtomicReference<Boolean> result = new AtomicReference<>(false);
        ThreadPoolExecutor executor = SpringUtils.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);

        CountDownLatch downLatch = new CountDownLatch(dto.size());
        for (OrderReceiveFromThirdDTO erpDTO : dto) {
            String tenantId = erpDTO.getTenantId();
            String supTenantId = erpDTO.getSupTenantId();
            if(StringUtils.isEmpty(tenantId)){
                errorOrderNo = errorOrderNo.append("租户信息异常");
                continue;
            }
            StringBuilder finalErrorOrderNo = errorOrderNo;
            executor.submit(() -> {
//
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getOneByTenantIdAndChannelType(tenantId, ChannelTypeEnum.Erp);
                erpDTO.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
                erpDTO.setTenantId(tenantId);
                erpDTO.setSupTenantId(supTenantId);
                try {
                    // 目前直接在这里进行多线程操作
                    Orders orders = new Orders();
                    orders.setChannelType(ChannelTypeEnum.Erp);
                    result.set(factory.getInvokeStrategy("erpOperationHandler")
                                      .formalTripartiteEntry(JSON.toJSONString(erpDTO), orders));

                } catch (Exception e) {
                    message.append(e.getMessage());
                    Integer code = ErpExceptionEnums.ALREADY_EXIST.getCode();
                    if (!result.get() && !String.valueOf(code).equals(message)) {
                        if (dto.size() - 1 == dto.indexOf(erpDTO)) {
                            finalErrorOrderNo.append(erpDTO.getOrderNo());
                        } else {
                            finalErrorOrderNo.append(erpDTO.getOrderNo()).append(",");
                        }
                    }
                    e.printStackTrace();
                } finally {
                    result.set(false);
                    downLatch.countDown();
                }
            });

        }
        // 用计数器
        downLatch.await();
        r.setSubCode("200");
        if (StringUtils.isNotEmpty(errorOrderNo)) {
            r.setSubCode("409");
            r.setMsg("部分订单录入失败,失败原因:"+message);
            r.setData(errorOrderNo);
        }

        return r;
    }

    @Override
    public void testThreadPool() {
        Orders order = new Orders();
        order.setOrderNo("test-01");
        log.info("录入测试");
        iOrdersService.save(order);
    }

    @Override
    public R tripartiteBatchReceiptForErp(List<ThirdReceiptDTO> dtos) throws InterruptedException {
        R r = R.ok();
        StringBuilder errorOrderNo = new StringBuilder();
        AtomicReference<Boolean> result = new AtomicReference<>(false);
        ThreadPoolExecutor executor = SpringUtils.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
        CountDownLatch downLatch = new CountDownLatch(dtos.size());
        for (ThirdReceiptDTO dto : dtos) {

            executor.submit(() -> {
                if (ObjectUtil.isEmpty(dto.getThirdChannelFlag())) {
                    dto.setThirdChannelFlag(thirdChannelFlag);
                }
                try {
                    //发货---分销商收货
                    result.set(factory.getInvokeStrategy("erpOperationHandler").tripartiteReceipt(dto));
                } catch (Exception e) {
                    if (!result.get()) {
                        if (dtos.size() - 1 == dtos.indexOf(dto)) {
                            errorOrderNo.append(dto.getOrderNo());
                        } else {
                            errorOrderNo.append(dto.getOrderNo()).append(",");
                        }
                    }
                    r.setMsg("部分订单录入失败,失败原因:" + e.getMessage());
                    e.printStackTrace();
                } finally {
                    result.set(false);
                    downLatch.countDown();
                }
            });


        }
        downLatch.await();
        if (StringUtils.isNotEmpty(errorOrderNo)) {
            r.setSubCode("409");

            r.setData(errorOrderNo);
        }
        return r;
    }

    /**
     * 功能描述：
     * 功能描述：初始化价格和站点(非会员)
     * 1.将所有基础表单清洗，生成站点信息,价格信息,币种符号信息(product_sku_price,rule_level_price,...)
     * 2.将所有业务价格表单清洗,生成站点信息,价格信息,币种符号信息(orders,order_items,order_item_price,...)
     * 3.默认清洗
     * 4.站点对应价格清洗
     * 5.顺序必须是先清洗指定商品及其相关业务,再清洗默认商品及其相关业务
     * 顺序:1.清洗指定站点  2.默认策略清洗站点(美国)
     *
     * @param sitePriceCleanBos 网站价格清洁bos
     * @param appointSwitch     指定产品清洗开关
     * @param acquiesceSwitch   默认产品价格清洗开关
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/12/23
     */
    @Override
    @IsDuplication
    @Transactional(rollbackFor = Exception.class)
    public Boolean initializePricesAndSites(List<SitePriceCleanBo> sitePriceCleanBos, boolean appointSwitch,
                                            boolean acquiesceSwitch) {
        List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list();
        // 指定清洗方案开关
        if(appointSwitch){
            // 1.对应的基础表单先清洗 2.清洗对应产品的订单站点信息 3.对应产品的价格信息不需要先清洗,历史订单的价格信息不需要清洗 4.非指定使用默认站点(建议分开做)
            Boolean isNeedCleanOrders = false;
            // siteCountryCurrencies 转换成map,key是siteId,value是SiteCountryCurrency
            Map<Long, SiteCountryCurrency> siteCountryCurrencyMap = siteCountryCurrencies.stream()
                                                                                         .collect(Collectors.toMap(SiteCountryCurrency::getId, Function.identity()));
            List<String> allItemNo = sitePriceCleanBos.stream()
                                                      .map(SitePriceCleanBo::getItemNos)
                                                      .flatMap(List::stream)
                                                      .collect(Collectors.toList());
            LambdaQueryWrapper<OrderItem> orderItemWrapper = new LambdaQueryWrapper<OrderItem>().in(OrderItem::getProductSkuCode, allItemNo);
            List<OrderItem> appointOrderItemList = TenantHelper.ignore(()->iOrderItemService.list(orderItemWrapper));
            if(CollUtil.isNotEmpty(appointOrderItemList)){
                isNeedCleanOrders = true;
            }

            // appointOrderItemList 转换成map,key是productSkuCode,value是对应的元素集合List<OrderItem>
            Map<String, List<OrderItem>> appointOrderItemMap=null;
            Map<String, Orders> ordersMap=null;
            List<OrderItemPrice> orderItemPriceList = null;
            Map<String, OrderItemPrice> orderItemPriceMap = null;
            if(isNeedCleanOrders){
                appointOrderItemMap = appointOrderItemList.stream().collect(Collectors.groupingBy(OrderItem::getProductSkuCode));

                List<String> orderNos = appointOrderItemList.stream()
                                                            .map(OrderItem::getOrderNo)
                                                            .distinct()
                                                            .collect(Collectors.toList());
                List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.list(new LambdaQueryWrapper<Orders>().in(Orders::getOrderNo, orderNos)));

                orderItemPriceList = TenantHelper.ignore(() -> iOrderItemPriceService.list(new LambdaQueryWrapper<OrderItemPrice>().in(OrderItemPrice::getOrderItemNo, allItemNo)));
                // orderItemPrices转换map,key是orderItemNo,value是对应的元素OrderItemPrice
                orderItemPriceMap = orderItemPriceList.stream()
                                                                                  .collect(Collectors.toMap(OrderItemPrice::getOrderItemNo, Function.identity()));

                // ordersList 转换成map,key是orderNo,value是对应的元素Orders
                ordersMap = ordersList.stream().collect(Collectors.toMap(Orders::getOrderNo, Function.identity()));
            }

            List<OrderItem> orderItems = new ArrayList<>();
            List<Orders> orders = new ArrayList<>();
            List<OrderItemPrice> orderItemPrices = new ArrayList<>();
//            List<ProductSkuPriceLog> productSkuPriceLogs = new ArrayList<>();
            List<ProductSkuPrice> productSkuPrices = new ArrayList<>();
            List<ProductReviewChangeDetail> productReviewChangeDetails = new ArrayList<>();
            for (SitePriceCleanBo sitePriceCleanBo : sitePriceCleanBos) {
                List<String> itemNos = sitePriceCleanBo.getItemNos();
                Long siteId = sitePriceCleanBo.getSiteId();
                SiteCountryCurrency currency = siteCountryCurrencyMap.get(siteId);

                for (String itemNo : itemNos) {
                    LambdaQueryWrapper<ProductReviewChangeDetail> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ProductReviewChangeDetail::getDelFlag, 0);
                    queryWrapper.eq(ProductReviewChangeDetail::getProductSkuCode, itemNo);
                    List<ProductReviewChangeDetail> changeDetails = iProductReviewChangeDetailService.list(queryWrapper);
                    ProductSku productSku = iProductSkuService.queryByProductSkuCode(itemNo);
                    if (productSku == null) {
                        log.error("商品SKU不存在, itemNo = {}", itemNo);
                        continue;
                    }
                    // 产品价格清洗-仅第一次可用,后续不可用,因为会生成多条记录
                    ProductSkuPrice productSkuPrice = TenantHelper.ignore(()->iProductSkuPriceService.queryByProductSkuCode(itemNo));
                    if (ObjectUtil.isNotEmpty(productSkuPrice)) {
                        productSkuPrice.setCountryCode(currency.getCountryCode());
                        productSkuPrice.setCurrency(currency.getCurrencyCode());
                        productSkuPrice.setCurrencySymbol(currency.getCurrencySymbol());
                        productSkuPrice.setSiteId(siteId);
                        productSkuPrices.add(productSkuPrice);
                    }
                    // 审核详情的站点信息清洗
                    if (ObjectUtil.isNotEmpty(changeDetails)) {
                        for (ProductReviewChangeDetail changeDetail : changeDetails) {
                            ProductReviewRecordVo reviewRecordVo = TenantHelper.ignore(() -> iProductReviewRecordService.queryById(changeDetail.getReviewRecordId()));
                            changeDetail.setSiteId(siteId);
                            cleanChangeDetails(productReviewChangeDetails, changeDetail, reviewRecordVo, productSkuPrice);
                        }
                    }
                    if(isNeedCleanOrders){
                        List<OrderItem> items = appointOrderItemMap.get(itemNo);
                        if (ObjectUtil.isNotEmpty(items)) {
                            for (OrderItem orderItem : items) {
                                orderItem.setSiteId(siteId);
                                orderItem.setCountryCode(currency.getCountryCode());
                                orderItem.setCurrency(currency.getCurrencyCode());
                                orderItem.setCurrencySymbol(currency.getCurrencySymbol());
                                orderItems.add(orderItem);

                                Orders order = ordersMap.get(orderItem.getOrderNo());
                                if(ObjectUtil.isNotEmpty(order)){
                                    order.setSiteId(siteId);
                                    order.setCountryCode(currency.getCountryCode());
                                    order.setCurrency(currency.getCurrencyCode());
                                    order.setCurrencySymbol(currency.getCurrencySymbol());
                                    orders.add(order);
                                }
                                OrderItemPrice orderItemPrice = orderItemPriceMap.get(orderItem.getOrderItemNo());
                                if (ObjectUtil.isNotEmpty(orderItemPrice)) {
                                    orderItemPrice.setSiteId(siteId);
                                    orderItemPrice.setCountryCode(currency.getCountryCode());
                                    orderItemPrice.setCurrency(currency.getCurrencyCode());
                                    orderItemPrice.setCurrencySymbol(currency.getCurrencySymbol());
                                    orderItemPrices.add(orderItemPrice);
                                }
                            }

                        }
                    }
                }

            }
            // 指定:产品-站点价格清洗
            iProductSkuPriceService.updateBatchById(productSkuPrices);
            // 指定:产品关联订单站点-符号-币种-国家清洗 orders order_items order_item_price
            if(CollUtil.isNotEmpty(orderItems)){
                iOrderItemService.updateBatchById(orderItems);
            }
            if(CollUtil.isNotEmpty(orders)){
                iOrdersService.updateBatchById(orders);
            }
            if(CollUtil.isNotEmpty(orderItemPrices)){
                iOrderItemPriceService.updateBatchById(orderItemPrices);
            }
            // 审核详情的站点信息清洗 product_review_change_detail 先洗指定产品价格清洗
            iProductReviewChangeDetailService.updateBatchById(productReviewChangeDetails);
            // 此处不需要price_sku_price_log的清洗, 默认清洗内会初始化操作类型
        }
        // 默认清洗方案开关
        if(acquiesceSwitch){
            // 默认:订单清洗,全部清洗成美国站点.
            LambdaQueryWrapper<ProductSkuPrice> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProductSkuPrice::getDelFlag, 0);
            List<ProductSkuPrice> productSkuPrices = TenantHelper.ignore(() -> iProductSkuPriceService.list(wrapper));
            // 拿siteCountryCurrencies内countryCode是US的站点,只有一个
            List<ProductSkuPrice> skuPrices = new ArrayList<>();
            List<Orders> newOrders = new ArrayList<>();
            List<OrderItem> newOrderItems = new ArrayList<>();
            List<OrderItemPrice> itemPrices = new ArrayList<>();
            List<ProductSkuPriceLog> productSkuPriceLogs = new ArrayList<>();
            List<ProductReviewChangeDetail> productReviewChangeDetails = new ArrayList<>();
            SiteCountryCurrency usSiteCountryCurrency = siteCountryCurrencies.stream()
                                                                             .filter(siteCountryCurrency -> "US".equals(siteCountryCurrency.getCountryCode()))
                                                                             .findFirst()
                                                                             .orElseThrow(() -> new IllegalStateException("No site with country code US found"));

            for (ProductSkuPrice productSkuPrice : productSkuPrices) {
                productSkuPrice.setSiteId(usSiteCountryCurrency.getId());
                productSkuPrice.setCountryCode(usSiteCountryCurrency.getCountryCode());
                productSkuPrice.setCurrency(usSiteCountryCurrency.getCurrencyCode());
                productSkuPrice.setCurrencySymbol(usSiteCountryCurrency.getCurrencySymbol());
                skuPrices.add(productSkuPrice);
            }
            LambdaQueryWrapper<Orders> ordersWrapper = new LambdaQueryWrapper<Orders>().isNull(Orders::getSiteId);
            List<Orders> orders = iOrdersService.list(ordersWrapper);
            for (Orders order : orders) {
                order.setSiteId(usSiteCountryCurrency.getId());
                order.setCountryCode(usSiteCountryCurrency.getCountryCode());
                order.setCurrency(usSiteCountryCurrency.getCurrencyCode());
                order.setCurrencySymbol(usSiteCountryCurrency.getCurrencySymbol());
                newOrders.add(order);
            }
            LambdaQueryWrapper<ProductReviewChangeDetail> notNullWrapper = new LambdaQueryWrapper<ProductReviewChangeDetail>().isNull(ProductReviewChangeDetail::getSiteId);
            List<ProductReviewChangeDetail> changeDetails = iProductReviewChangeDetailService.list(notNullWrapper);
            for (ProductReviewChangeDetail changeDetail : changeDetails) {
                ProductReviewRecordVo reviewRecordVo = TenantHelper.ignore(() -> iProductReviewRecordService.queryById(changeDetail.getReviewRecordId()));
                if(ObjectUtil.isNotEmpty(changeDetail.getProductSkuCode())){
                    ProductSkuPrice productSkuPrice = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCode(changeDetail.getProductSkuCode()));
                    changeDetail.setSiteId(usSiteCountryCurrency.getId());
                    if(ObjectUtil.isEmpty(productSkuPrice)){
                        log.error("产品SKU不存在, itemNo = {}", changeDetail.getProductSkuCode());
                    }
                    cleanChangeDetails(productReviewChangeDetails, changeDetail, reviewRecordVo, productSkuPrice);
                }
            }
            LambdaQueryWrapper<OrderItem> orderItemLambdaQueryWrapper = new LambdaQueryWrapper<OrderItem>().isNull(OrderItem::getSiteId);
            List<OrderItem> orderItems = iOrderItemService.list(orderItemLambdaQueryWrapper);
            for (OrderItem orderItem : orderItems) {

                orderItem.setSiteId(usSiteCountryCurrency.getId());
                orderItem.setCountryCode(usSiteCountryCurrency.getCountryCode());
                orderItem.setCurrency(usSiteCountryCurrency.getCurrencyCode());
                orderItem.setCurrencySymbol(usSiteCountryCurrency.getCurrencySymbol());
                newOrderItems.add(orderItem);
            }

            LambdaQueryWrapper<OrderItemPrice> priceLambdaQueryWrapper = new LambdaQueryWrapper<OrderItemPrice>().isNull(OrderItemPrice::getSiteId);
            List<OrderItemPrice> orderItemPrices = TenantHelper.ignore(() -> iOrderItemPriceService.list(priceLambdaQueryWrapper));
            for (OrderItemPrice orderItemPrice : orderItemPrices) {
                orderItemPrice.setSiteId(usSiteCountryCurrency.getId());
                orderItemPrice.setCountryCode(usSiteCountryCurrency.getCountryCode());
                orderItemPrice.setCurrency(usSiteCountryCurrency.getCurrencyCode());
                orderItemPrice.setCurrencySymbol(usSiteCountryCurrency.getCurrencySymbol());
                itemPrices.add(orderItemPrice);
            }
            // 默认都给update
            List<ProductSkuPriceLog> list = iProductSkuPriceLogService.list();
            for (ProductSkuPriceLog productSkuPriceLog : list) {
                productSkuPriceLog.setLogType(PriceOperateLog.Update.name());
                productSkuPriceLogs.add(productSkuPriceLog);
            }
            iOrdersService.updateBatchById(newOrders);
            iProductSkuPriceService.updateBatchById(skuPrices);
            iOrderItemService.updateBatchById(newOrderItems);
            // 默认:订单清洗,全部清洗成美国站点.还有价格价格变动记录表product_review_change_detail
            iProductReviewChangeDetailService.updateBatchById(productReviewChangeDetails);
            iOrderItemPriceService.updateBatchById(itemPrices);
            iProductSkuPriceLogService.updateBatchById(productSkuPriceLogs);
        }



        return Boolean.TRUE;

    }

    /**
     * 功能描述：清洗更改详细信息
     *
     * @param productReviewChangeDetails 产品审查变更详情
     * @param changeDetail               更改详细信息
     * @param reviewRecordVo             审核记录vo
     * @param productSkuPrice            产品sku价格
     * <AUTHOR>
     * @date 2025/01/02
     */
    private void cleanChangeDetails(List<ProductReviewChangeDetail> productReviewChangeDetails,
                                    ProductReviewChangeDetail changeDetail, ProductReviewRecordVo reviewRecordVo,
                                    ProductSkuPrice productSkuPrice) {
        changeDetail.setProductSkuPriceId(productSkuPrice.getId());
        if(ProductReviewTypeEnum.NewProduct.equals(reviewRecordVo.getReviewType())){
            changeDetail.setReviewType(PriceOperateLog.Add.name());
        }
        if(ProductReviewTypeEnum.NewProductSku.equals(reviewRecordVo.getReviewType())){
            changeDetail.setReviewType(PriceOperateLog.Add.name());
        }
        if(ProductReviewTypeEnum.Price.equals(reviewRecordVo.getReviewType())){
            changeDetail.setReviewType(PriceOperateLog.Update.name());
        }
        productReviewChangeDetails.add(changeDetail);
    }
}
