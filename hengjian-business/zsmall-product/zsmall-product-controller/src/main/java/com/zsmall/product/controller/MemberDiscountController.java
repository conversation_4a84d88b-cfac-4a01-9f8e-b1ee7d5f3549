package com.zsmall.product.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.system.domain.vo.SysDictDataVo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.service.ISysDictDataService;
import com.hengjian.system.service.ISysTenantService;
import com.hengjian.system.service.ISysUserService;
import com.zsmall.bma.open.member.service.IMemberDiscountV2Service;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.calculate.entity.util.DeliveryFeeV2Utils;
import com.zsmall.common.domain.DeliveryFeeByErpRequest;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.member.MemberDiscountBo;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuWarehouseByCountryCode;
import com.zsmall.product.entity.domain.vo.member.MemberDiscountVo;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.ITenantShippingAddressService;
import com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseAddressService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员等级折扣
 *
 * <AUTHOR> Li
 * @date 2024-07-25
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/memberDiscount")
public class MemberDiscountController extends BaseController {
    private final IMemberDiscountV2Service memberDiscountService;
    private final ISysUserService sysUserService;
    private final ISysDictDataService dictDataService;
    private final IProductSkuService iProductSkuService;
    private final ISysTenantService sysTenantService;
    private final ITenantShippingAddressService shippingAddressService;
    private final IWarehouseService warehouseService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final IProductSkuPriceService productSkuPriceService;
    private final IWarehouseAddressService warehouseAddressService;
    private final IProductSkuService productSkuService;
    private final DeliveryFeeV2Utils deliveryFeeV2Utils;
    private final PriceSupportV2 priceSupportV2;
    private final SiteCountryCurrencyMapper siteCountryCurrencyMapper;
    /**
     * 查询会员等级折扣列表
     */
    @SaCheckPermission("system:memberDiscount:list")
    @GetMapping("/list")
    public TableDataInfo<MemberDiscountVo> list(MemberDiscountBo bo, PageQuery pageQuery) {
        TableDataInfo<MemberDiscountVo> memberDiscountVoTableDataInfo = memberDiscountService.queryPageList(bo, pageQuery);
        memberDiscountVoTableDataInfo.getRows().forEach(item -> {
            String createBy = item.getCreateBy();
            if (ObjUtil.isNotEmpty(createBy)){
                SysUserVo sysUserVo = sysUserService.selectUserById(Long.valueOf(createBy));
                if (Objects.nonNull(sysUserVo)){
                    item.setCreateBy(sysUserVo.getNickName());
                }
            }
        });
        return memberDiscountVoTableDataInfo;
    }

    /**
     * 导出会员等级折扣列表
     */
    @SaCheckPermission("system:memberDiscount:export")
    @Log(title = "会员等级折扣", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MemberDiscountBo bo, HttpServletResponse response) {
        List<MemberDiscountVo> list = memberDiscountService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员等级折扣", MemberDiscountVo.class, response,false);
    }

    /**
     * 获取会员等级折扣详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:memberDiscount:query")
    @GetMapping("/{id}")
    public R<MemberDiscountVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(memberDiscountService.queryById(id));
    }

    /**
     * 新增会员等级折扣
     */
    @SaCheckPermission("system:memberDiscount:add")
    @Log(title = "会员等级折扣", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MemberDiscountBo bo) {
        //查询当前会员系数是不是已经关联
        SysDictDataVo sysDictDataVo = dictDataService.selectDictDataById(bo.getDictCode());
        if (ObjUtil.isNull(sysDictDataVo)){
            return R.fail(StrUtil.format("会员等级字典数据未查询到：{}",bo.getDictCode()));
        }
        bo.setMemberName(sysDictDataVo.getDictLabel());
        return toAjax(memberDiscountService.insertByBo(bo));
    }

    /**
     * 修改会员等级折扣
     */
    @SaCheckPermission("system:memberDiscount:edit")
    @Log(title = "会员等级折扣", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MemberDiscountBo bo) {
        return toAjax(memberDiscountService.updateByBo(bo));
    }

    /**
     * 删除会员等级折扣
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:memberDiscount:remove")
    @Log(title = "会员等级折扣", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(memberDiscountService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 获取ERP尾程派送费
     * @param deliveryFeeRequest 请求对象
     * @param businessType 询价类型 1: 商城商品询价， 2:测算器询价
     * @return
     */
    @Log(title = "商城尾程派送费测算", businessType = BusinessType.OTHER)
    @PostMapping("/getDeliveryFeeByErp/{businessType}")
    public R getDeliveryFeeByErp(@RequestBody DeliveryFeeRequest deliveryFeeRequest,@PathVariable(value = "businessType") Integer businessType) {
        try {
            if (ObjectUtil.isNull(LoginHelper.getTenantId())){
                throw new RuntimeException("请先登录在使用");
            }
            if (ObjUtil.isNull(businessType)) {
                throw new RuntimeException("算价类型不能为空1: 商城商品测算,2:测算器测算");
            }
            //非分销商只能用测算器
            if (ObjectUtil.equal(LoginHelper.getTenantType(), TenantType.Distributor.name())) {
                //判断当前登录的租户是否支持测算
                Boolean approvedTenant = sysTenantService.getIsApprovedTenant(LoginHelper.getTenantId(), 1);
                if (!approvedTenant&&businessType==1){
                    throw new RuntimeException("当前分销商不支持商品测算");
                }
            }else {
                //只支持测算器
                if (!ObjectUtil.equal(2,businessType)){
                    throw new RuntimeException("供应商/超管只支持测算器测算");
                }
            }

            List<DeliveryFeeByErpRequest> deliveryFeeByErpRequests = new ArrayList<>();
            //校验询价前参数封装询价
            DeliveryFeeByErpRequest deliveryFeeByErpRequest = validDeliveryFeeByErp(deliveryFeeRequest, businessType);
            //校验商品信息
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.queryByProductSkuCode(deliveryFeeRequest.getProductSkuCode()));
            deliveryFeeByErpRequests.add(deliveryFeeByErpRequest);
            //询价
            List<DeliveryFeeByErpResponse> deliveryFeeByErpResponses = deliveryFeeV2Utils.getDeliveryFeeByErp(deliveryFeeByErpRequests,productSku.getTenantId());
            DeliveryFeeByErpResponse deliveryFeeByErpResponse = deliveryFeeByErpResponses.get(0);
            LambdaQueryWrapper<SiteCountryCurrency> sc = new LambdaQueryWrapper<>();
            sc.eq(SiteCountryCurrency::getCurrencyCode, deliveryFeeByErpResponse.getCurrencyCode());
            sc.eq(SiteCountryCurrency::getDelFlag,0);
            sc.groupBy(SiteCountryCurrency::getCurrencyCode);
            SiteCountryCurrency scc = siteCountryCurrencyMapper.selectOne(sc);
            if (ObjectUtil.isNull(scc)){
                throw new RuntimeException("币种不匹配");
            }
            //测算器
            if (businessType==2){
                DeliveryFeeResponse deliveryFeeResponse=new DeliveryFeeResponse();
                deliveryFeeResponse.setDeliveryFee(deliveryFeeByErpResponse.getShippingFee());
                //币种单位
                deliveryFeeResponse.setCurrencySymbol(scc.getCurrencySymbol());
                return R.ok(deliveryFeeResponse);
            }else {
                //询价结束获取会员价
                ProductPriceResponse productPrice = priceSupportV2.getProductPriceBySite(deliveryFeeRequest.getProductSkuCode(), deliveryFeeByErpResponse.getShippingFee(), LoginHelper.getTenantId(), deliveryFeeRequest.getSite());
                DeliveryFeeResponse deliveryFeeResponse = BeanUtil.toBean(productPrice, DeliveryFeeResponse.class);
                //封装仓库信息
                LambdaQueryWrapper<Warehouse> la = new LambdaQueryWrapper<>();
                la.eq(Warehouse::getWarehouseCode, deliveryFeeByErpResponse.getOrgWarehouseCode());
                la.eq(Warehouse::getTenantId, productSku.getTenantId());
                Warehouse one = TenantHelper.ignore(() -> warehouseService.getOne(la));
                //获取地址信息
                if (ObjectUtil.isNotNull(one)){
                    deliveryFeeResponse.setAddress(warehouseService.getWarehouseAddressInfo(one.getWarehouseSystemCode()));
                }
                //币种单位
                deliveryFeeResponse.setCurrencySymbol(scc.getCurrencySymbol());
                return R.ok(deliveryFeeResponse);
            }

        } catch (Exception e) {
            log.error(StrUtil.format("[获取ERP尾程派送费失败]：{}", e));
            String receivingCountryCode = deliveryFeeRequest.getReceivingCountryCode();
            DeliveryFeeResponse d=new DeliveryFeeResponse();
            if (StrUtil.isNotEmpty(receivingCountryCode)){
                LambdaQueryWrapper<SiteCountryCurrency> q = new LambdaQueryWrapper<>();
                q.eq(SiteCountryCurrency::getCountryCode, receivingCountryCode);
                SiteCountryCurrency siteCountryCurrency = siteCountryCurrencyMapper.selectOne(q);
                if (ObjectUtil.isNotNull(siteCountryCurrency)){
                    d.setCurrencySymbol(siteCountryCurrency.getCurrencySymbol());
                }
            }
            return R.fail(d,504,"获取尾程派送费失败！","测算失败,"+ e.getMessage());
        }
    }



    /**
     * @description:  校验前端询价参数
     * @author: len
    *  @date: 2024/8/4 14:44
     * @param: deliveryFeeRequest  询价请求对象
     * @param: businessType 询价类型 1: 发货仓未知/收货邮编未知算价， 2:发货仓未知/收货邮编已知   3:发货仓已知/收货邮编已知 4:询价计算器
     * @return: com.zsmall.product.entity.domain.DeliveryFeeByErpRequest  询价请求对象
     **/
    DeliveryFeeByErpRequest validDeliveryFeeByErp(DeliveryFeeRequest deliveryFeeRequest, Integer businessType) {
        switch (businessType){
            case 1:
                if (StrUtil.isEmpty(deliveryFeeRequest.getSite())){
                    throw new RuntimeException("商品测算,站点不能为空");
                }
                break;
            case 2:
                if (StrUtil.isEmpty(deliveryFeeRequest.getDeliveryWarehouseCode())){
                    throw new RuntimeException("测算器,发货仓库编码不能为空");
                }
                break;
            default:
                if (StrUtil.isEmpty(deliveryFeeRequest.getReceivingCountryCode())){
                    throw new RuntimeException("商品测算,收货国家编码不能为空");
                }
                if (StrUtil.isEmpty(deliveryFeeRequest.getProductSkuCode())){
                    throw new RuntimeException("测算模式，商品编码不能为空");
                }
                if (!"US".equals(deliveryFeeRequest.getReceivingCountryCode()) || ObjectUtil.isEmpty(deliveryFeeRequest.getReceivingPostcode())) {
                    throw new RuntimeException("[获取ERP尾程派送费], US国家测算邮编不能为空!");
                }
                break;
        }
        //请求商品信息
        String productSkuCode = deliveryFeeRequest.getProductSkuCode();
        if (ObjUtil.isNull(productSkuCode)) {
            throw new RuntimeException("商品skuId不能为空");
        }
        //校验商品信息
        ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.queryByProductSkuCode(productSkuCode));
        if (ObjectUtil.isNull(productSku)) {
            throw new RuntimeException(StrUtil.format("sku:{}，的商品信息不存在", productSkuCode));
        }
        // 判断当前商品在当前站点是否有价格
        LambdaQueryWrapper<ProductSkuPrice> q = new LambdaQueryWrapper<>();
        q.eq(ProductSkuPrice::getProductSkuCode, productSkuCode);
        q.eq(ProductSkuPrice::getCountryCode, deliveryFeeRequest.getReceivingCountryCode());
        ProductSkuPrice priceServiceOne = productSkuPriceService.getOne(q);
        if (ObjectUtil.isNull(priceServiceOne) || ObjUtil.isNull(priceServiceOne.getPlatformPickUpPrice())){
            throw new RuntimeException(StrUtil.format("SKU:{},在此地区:{},未维护价格", productSkuCode,deliveryFeeRequest.getReceivingCountryCode()));
        }

        DeliveryFeeByErpRequest deliveryFeeByErpRequest = new DeliveryFeeByErpRequest();
        //封装ERP询价商品请求对象
        DeliveryFeeByErpRequest.ProductItem skuList = new DeliveryFeeByErpRequest.ProductItem();
        skuList.setErpSku(productSku.getSku());
        skuList.setQuantity(1);
        deliveryFeeByErpRequest.setSkuList(List.of(skuList));
        deliveryFeeByErpRequest.setCountryCode(deliveryFeeRequest.getReceivingCountryCode());
        deliveryFeeByErpRequest.setSupplierTenantId(productSku.getTenantId());
        //获取店铺信息
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(LoginHelper.getTenantId());
        if (ObjectUtil.isNull(sysTenantVo)) {
            throw new RuntimeException(StrUtil.format("店铺信息不存在,租户ID:{}", LoginHelper.getTenantId()));
        }
        if (ObjectUtil.isEmpty(sysTenantVo.getThirdChannelFlag())) {
            throw new RuntimeException(StrUtil.format("分销商:{}店铺信息不存在", LoginHelper.getTenantId()));
        }
        //封装渠道店铺信息
        deliveryFeeByErpRequest.setChannelFlag(sysTenantVo.getThirdChannelFlag());
        deliveryFeeByErpRequest.setPostcode(deliveryFeeRequest.getReceivingPostcode());
        //1:商城测算
        if (businessType == 1) {
            //根据收货地址国家匹配仓库
            List<ProductSkuWarehouseByCountryCode> warehouseCodeBySite = productSkuPriceService.getBaseMapper()
                                                                                               .getWarehouseCodeBySite(productSkuCode,  deliveryFeeRequest.getReceivingCountryCode());
            if (CollUtil.isEmpty(warehouseCodeBySite)){
                throw new RuntimeException(StrUtil.format("SKU:{},收货地址国家:{},未匹配到仓库", productSkuCode,deliveryFeeRequest.getReceivingCountryCode()));
            }
            List<String> filteredWarehouseCodes = warehouseCodeBySite.stream()
                                                      // 筛选出dropShippingStock大于0的ProductSkuWarehouseByCountryCode对象
                                                      .filter(productSkuWarehouse -> productSkuWarehouse.getDropShippingStock() > 0)
                                                      // 提取warehouseCode
                                                      .map(ProductSkuWarehouseByCountryCode::getWarehouseCode)
                                                      // 收集结果到List
                                                      .collect(Collectors.toList());
            if (CollUtil.isEmpty(filteredWarehouseCodes)){
                throw new RuntimeException(StrUtil.format("SKU:{},收货地址国家:{},未匹配到有库存的仓库", productSkuCode,deliveryFeeRequest.getReceivingCountryCode()));
            }
            //封装有库存的发货仓信息
            deliveryFeeByErpRequest.setOrgWarehouseCodeList(filteredWarehouseCodes);
        }else {
            //测算器
            deliveryFeeByErpRequest.setOrgWarehouseCodeList(List.of(deliveryFeeRequest.getDeliveryWarehouseCode()));
        }
        deliveryFeeByErpRequest.setRequestId(IdUtil.getSnowflakeNextIdStr());
        return deliveryFeeByErpRequest;
    }



}
