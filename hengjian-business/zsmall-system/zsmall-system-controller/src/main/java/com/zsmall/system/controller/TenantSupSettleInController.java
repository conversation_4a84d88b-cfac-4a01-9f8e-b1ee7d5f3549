package com.zsmall.system.controller;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.system.biz.service.ITenantDistrExtendService;
import com.zsmall.system.biz.service.TenantSupSettleInService;
import com.zsmall.system.entity.domain.bo.settleInBasic.ReviewRecordBo;
import com.zsmall.system.entity.domain.bo.settleInBasic.SupSettleInGetBo;
import com.zsmall.system.entity.domain.bo.settleInBasic.SupSettleInInfoBo;
import com.zsmall.system.entity.domain.vo.settleInBasic.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 供应商入驻
 */

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/tenantSupSettleIn")
public class TenantSupSettleInController {

    private final TenantSupSettleInService tenantSupSettleInService;
    private final ITenantDistrExtendService iTenantDistrExtendService;

    /**
     * 供应商入驻信息保存
     */
    @PostMapping("/saveSupSettlementInfo")
    public R<Void> saveSupSettlementInfo(@RequestBody SupSettleInInfoBo bo) throws Exception {
        return tenantSupSettleInService.saveSupSettleIn(bo);
    }

    /**
     * 修改供应商入驻基础信息
     */
    @PostMapping("/updateExtendInfoBasic")
    public R<Void> updateSupExtendInfoBasic(@RequestBody SupSettleInBasicBo bo) throws Exception {
        return tenantSupSettleInService.updateSupSettleInBasic(bo);
    }

    /**
     * 修改供应商入驻联系人信息
     */
    @PostMapping("/updateExtendInfoContact")
    public R<Void> updateExtendInfoContact(@RequestBody ContactBo bo) throws Exception {
        return tenantSupSettleInService.updateSupSettleInContact(bo);
    }

    /**
     * 获取供应商入驻信息
     */
    @GetMapping("/getSupExtendInfoResp")
    public R<SupBasicVo> getSupExtendInfoResp(SupSettleInGetBo bo) throws IllegalAccessException {
        return tenantSupSettleInService.getSupSettleInResp(bo);
    }

    /**
     * 获取供应商基础信息
     */
    @GetMapping("/getBasicInfoResp")
    public R<SupSettleInBasicBo> getBasicInfoResp() {
        return tenantSupSettleInService.getBasicInfoResp();
    }

    /**
     * 获取供应商公司联系人信息
     */
    @GetMapping("/getContactInfoResp")
    public R<ContactBo> getContactInfoResp() {
        return tenantSupSettleInService.getContactInfoResp();
    }

    /**
     * 获取员工审核记录信息
     */
    @GetMapping("/getReviewRecordManager")
    public TableDataInfo<ReviewRecordManagerBo> getReviewRecordManager(ReviewRecordBo bo, PageQuery pageQuery) {
        return tenantSupSettleInService.getReviewRecordManager(bo, pageQuery);
    }

    /**
     * 功能描述：管理员审批记录导出
     *
     * @param bo        Bo
     * @param pageQuery 页面查询
     * @param response  响应
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2025/09/08
     */
    @GetMapping(value = "/reviewRecordManagerExport")
    public R<Void> reviewRecordManagerExport(ReviewRecordBo bo, PageQuery pageQuery, HttpServletResponse response) throws RStatusCodeException {
//        productService.exportAsync(bo, pageQuery, response);
//        return R.ok();
//        String tenantType = LoginHelper.getTenantType();
//        if (StrUtil.isBlank(tenantType)) {
//            return R.fail("请先登录");
//        }
//        if (ObjectUtil.notEqual(LoginHelper.getTenantType(), TenantType.Manager.name())){
//            return R.fail("商品导出只支持管理员");
//        }
        bo.setTenantId(LoginHelper.getTenantId());
        tenantSupSettleInService.reviewRecordManagerExport(bo,pageQuery,response);
        return R.ok();
    }
    /**
     * 修改员工审核记录信息
     */
    @PostMapping("/updateReviewRecordMd")
    public R<Void> updateReviewRecordMd(@RequestBody ReviewRecordBo bo) {
        R<Void> voidR = tenantSupSettleInService.updateReviewRecordMd(bo);

        iTenantDistrExtendService.pushCompanyToErp(voidR,bo,Boolean.FALSE);
        return voidR;
    }

    /**
     * 批量通过员工审核供应商入驻信息
     *
     * @param reviewRecordBoList
     * @return
     */
    @PostMapping("/batchUpdateReviewRecordMd")
    public R<Void> batchUpdateReviewRecordMd(@RequestBody List<ReviewRecordBo> reviewRecordBoList){
        Boolean result = tenantSupSettleInService.batchUpdateReviewRecordMd(reviewRecordBoList);
        R r = null;
        if(result){
            r = R.ok();
        }
        for (ReviewRecordBo bo : reviewRecordBoList) {
            iTenantDistrExtendService.pushCompanyToErp(r,bo,Boolean.FALSE);
        }
        return R.ok();
    }


    /**
     * 获取当前用户的入驻信息是否完善状态
     */
    @PostMapping("/getIsPerfectInfoStatus")
    public R<SupSettleInPerfectStateBoVo> getIsPerfectInfoStatus() {
        return tenantSupSettleInService.getIsPerfectInfoStatus();
    }

    @GetMapping("/setIsPerfectInfoSign")
    public R<Void> setIsPerfectInfoSign() {
        tenantSupSettleInService.setIsPerfectInfoSign();
        return R.ok();
    }

    @GetMapping("/setIsPerfectInfoSignNew")
    public R<Void> setIsPerfectInfoSignNew(@RequestParam("reviewState") String reviewState) {
        tenantSupSettleInService.setIsPerfectInfoSignNew(reviewState);
        return R.ok();
    }
}
