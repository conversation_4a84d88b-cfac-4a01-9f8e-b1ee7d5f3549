package com.zsmall.lottery.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.service.ISysInfService;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.activity.entity.domain.dto.productActivity.*;
import com.zsmall.activity.entity.iservice.*;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.IMemberDiscountV2Service;
import com.zsmall.bma.open.member.service.impl.RuleLevelProductPriceV2ServiceImpl;
import com.zsmall.bma.open.member.support.MemberV2Support;
import com.zsmall.calculate.entity.support.DeliveryFeeSupport;
import com.zsmall.calculate.entity.util.DeliveryFeeV2Utils;
import com.zsmall.common.domain.DeliveryFeeByErpRequest;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.exception.DeliveryFeeException;
import com.zsmall.common.exception.StockException;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.OrderItemProductSku;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.iservice.IOrderItemPriceService;
import com.zsmall.order.entity.iservice.IOrderItemProductSkuService;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/27 11:27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PriceSupportV2 {
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final ISysConfigService sysConfigService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuService iProductSkuService;
    private final IProductService iProductService;
    private final IOrderItemPriceService iOrderItemPriceService;
    // 会员活动入口
    private final RuleLevelProductPriceV2ServiceImpl ruleLevelProductPriceService;
    private final IMemberRuleRelationV2ServiceImpl iMemberRuleRelationService;
    private final IMemberLevelV2ServiceImpl iMemberLevelService;
    private final MemberV2Support memberSupport;
   // 测算入口
    private final DeliveryFeeSupport deliveryFeeSupport;
    private final DeliveryFeeV2Utils deliveryFeeUtils;
    private final ProductSkuStockMapper productSkuStockMapper;
    private final IWarehouseService iWarehouseService;
    private final ISysInfService sysInfService;
    // 基础框架业务不会依赖 基础业务
    private final ISysTenantService sysTenantService;
    private final IProductSkuService productSkuService;
    private final IMemberDiscountV2Service iMemberDiscountV2Service;
    private final IOrderItemProductSkuService orderItemProductSkuService;
    private final IDistributorProductActivityService distributorProductActivityService;
    private final ISupplierProductActivityService supplierProductActivityService;
    private final ISupplierProductActivityPriceService supplierProductActivityPriceService;
    private final IDistributorProductActivityStockService distributorProductActivityStockService;
    private final IDistributorProductActivityPriceService distributorProductActivityPriceService;
    private final IOrderItemService iOrderItemService;
    private final IOrdersService ordersService;

    @InMethodLog("设置订单价格-根据活动")
    private OrderItemPrice activityFlow(OrderPriceCalculateDTO paramDTO,String activityCode, String productSkuCode, OrderItemPrice orderItemPrice,
                                        LogisticsTypeEnum logisticsType, Integer totalQuantity, OrderItem orderItem,String tenantId,
                                        String logisticsCompanyName,Orders order,LocaleMessage localeMessage, String zipCode,List<String> stashList) {
        DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() -> distributorProductActivityService.getOne(Wrappers.<DistributorProductActivity>lambdaQuery()
                                                                                                                 .eq(DistributorProductActivity::getDistributorActivityCode, activityCode)));
        if(null == distributorProductActivity || null == distributorProductActivity.getSupplierActivityId()){
            log.info("活动不存在 activityCode = {}", activityCode);
            return orderItemPrice;
        }
        Long supplierActivityId  = distributorProductActivity.getSupplierActivityId();
        if(null == supplierActivityId){
            log.info("供应商活动不存在 supplierActivityId = {}",supplierActivityId);
           return orderItemPrice;
        }
        SupplierProductActivity supplierProductActivity = TenantHelper.ignore(() -> supplierProductActivityService.getOne(Wrappers.<SupplierProductActivity>lambdaQuery()
                                                                                .eq(SupplierProductActivity::getId, supplierActivityId)));
        if(null == supplierProductActivity || null == supplierProductActivity.getSupplierActivityCode()){
            log.info("供应商活动不存在 supplierActivityId = {}", supplierActivityId);
            return orderItemPrice;
        }
        SupplierProductActivityPrice supplierProductActivityPrice = TenantHelper.ignore(() -> supplierProductActivityPriceService.getOne(Wrappers.<SupplierProductActivityPrice>lambdaQuery()
                                                                                              .eq(SupplierProductActivityPrice::getSupplierActivityCode, supplierProductActivity.getSupplierActivityCode())));
        if(null == supplierProductActivityPrice || null == supplierProductActivityPrice.getSupplierActivityCode()){
            log.info("供应商活动价格不存在 supplierActivityCode = {}", supplierProductActivity.getSupplierActivityCode());
            return orderItemPrice;
        }
        DistributorProductActivityPrice distributorProductActivityPrice = TenantHelper.ignore(() -> distributorProductActivityPriceService.getOne(Wrappers.<DistributorProductActivityPrice>lambdaQuery()
                                                                                                                                 .eq(DistributorProductActivityPrice::getDistributorActivityId, distributorProductActivity.getId())));
        if(null == distributorProductActivityPrice){
            log.info("分销商活动价格不存在 distributorActivityCode = {}", distributorProductActivity.getDistributorActivityCode());
            return orderItemPrice;
        }
        List<DistributorProductActivityStock> distributorProductActivityStockList = TenantHelper.ignore(() -> distributorProductActivityStockService.list(Wrappers.<DistributorProductActivityStock>lambdaQuery()
                                                                                                    .eq(DistributorProductActivityStock::getDistributorActivityId, distributorProductActivity.getId())));
        if(null == distributorProductActivityStockList){
            log.info("供应商活动库存不存在 distributorActivityId = {}", distributorProductActivity.getId());
            return orderItemPrice;
        }
        // 将distributorProductActivityStockList中全部的WarehouseCode提取出来组成一个集合并去重
        List<String> activityWarehouseCodeList = distributorProductActivityStockList.stream().map(DistributorProductActivityStock::getWarehouseCode).distinct().collect(Collectors.toList());

        ProductSkuPrice productSkuPrice = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,order.getSiteId()));
        ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getById(productSkuPrice.getProductSkuId()));
        // 测算相关逻辑
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(tenantId, 1);
        // erp仓库
        String orgWarehouseCode = null;
        // 物流商集合
        List<String> carrierList = new ArrayList<>();
        // 订单国家
        String countryCode = order.getCountryCode();
        // erp尾程派送费 默认活动尾程派送费
        BigDecimal finalDeliveryFeeFromErp = distributorProductActivityPrice.getDistributorActivityFinalDeliveryFee();
        // 供应商租户id
        String supTenantId = productSku.getTenantId();
        if(approvedTenant && (LogisticsTypeEnum.DropShipping.equals(logisticsType))){
            try{
            boolean aCase = false;
            ChannelTypeEnum channelType = order.getChannelType();
            carrierList = getCarrier(logisticsCompanyName, channelType, aCase, carrierList);
            DeliveryFeeByErpResponse deliveryFeeFromErp = null;
            // 仓库可配送国家判断
            List<Warehouse> deliverableWarehouse = new ArrayList<>();
            List<String> warehouseCodeList = new ArrayList<>();
            for(String warehouseCode : activityWarehouseCodeList){
                LambdaQueryWrapper<Warehouse> eq = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, supTenantId)
                                                                                      .eq(Warehouse::getWarehouseCode, warehouseCode)
                                                                                      .eq(Warehouse::getDelFlag, 0);
                List<Warehouse> warehouses = TenantHelper.ignore(() -> iWarehouseService.list(eq));
                if(CollUtil.isNotEmpty(warehouses)){
                    for (Warehouse warehouse : warehouses){
                        List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(warehouse.getWarehouseSystemCode()));
                        if(CollUtil.isNotEmpty(countryCodeList)){
                            if(countryCodeList.contains(countryCode)){
                                deliverableWarehouse.add(warehouse);
                                warehouseCodeList.add(warehouseCode);
                            }
                        }
                    }
                }
            }
            // 判断可配送订单的仓库
            if(CollUtil.isEmpty(deliverableWarehouse)){
                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON();
                order.setPayErrorMessage(errorMsg);
                order.setExceptionCode(OrderExceptionEnum.measurement_anomaly.getValue());
                localeMessage.append(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                order.setPlatformTotalFinalDeliveryFee(null);
                order.setOriginalTotalFinalDeliveryFee(null);
                return orderItemPrice;
            }else {
                deliveryFeeFromErp = getDeliveryFeeFromErp(warehouseCodeList, zipCode, productSkuCode, carrierList, tenantId, supTenantId, countryCode, zipCode);

                finalDeliveryFeeFromErp = deliveryFeeFromErp.getShippingFee();
                orgWarehouseCode = deliveryFeeFromErp.getOrgWarehouseCode();
                String carrierCode = deliveryFeeFromErp.getCarrierCode();
                String logisticsCode = deliveryFeeFromErp.getLogisticCode();
                if (ObjectUtil.isEmpty(finalDeliveryFeeFromErp)) {
                    throw new DeliveryFeeException("erp接口异常");
                }
                LambdaQueryWrapper<Warehouse> warehouseLambdaQueryWrapper = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, supTenantId)
                                                                                      .eq(Warehouse::getWarehouseCode, orgWarehouseCode)
                                                                                      .eq(Warehouse::getDelFlag, 0);
                List<Warehouse> warehousesByEro = TenantHelper.ignore(() -> iWarehouseService.list(warehouseLambdaQueryWrapper));
                if (CollUtil.isEmpty(warehousesByEro)||warehousesByEro.size()>1){
                    throw new StockException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS.args(orgWarehouseCode));
                }
                Warehouse warehouse = warehousesByEro.get(0);

                paramDTO.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                paramDTO.setLogisticsCarrierCode(carrierCode);
                paramDTO.setLogisticsCode(logisticsCode);
                paramDTO.setWarehouseCode(orgWarehouseCode);
            }
            // 获取最终尾程派送费，考虑会员折扣
            finalDeliveryFeeFromErp = getOriginalFinalDeliveryFeeAfterDiscount(supTenantId,tenantId,finalDeliveryFeeFromErp);

            }catch (DeliveryFeeException e){
                if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                    order.setExceptionCode(OrderExceptionEnum.final_delivery_fee_exception.getValue());
                    try {
                        throw new DeliveryFeeException(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION.args(productSkuCode));
                    } catch (DeliveryFeeException ex) {
                        LocaleMessage localeMessage2 = ex.getLocaleMessage();
                        JSONObject errorMsg = localeMessage2.toJSON();
                        order.setPayErrorMessage(errorMsg);
                        order.setOrderState(OrderStateType.Failed);
                        localeMessage.append(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION.args(productSkuCode));
                    }
                }
            }catch (StockException e){
                if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                    order.setExceptionCode(OrderExceptionEnum.stock_same_warehouse_code_exists.getValue());
                    LocaleMessage localeMessage2 = e.getLocaleMessage();
                    JSONObject errorMsg = localeMessage2.toJSON();
                    order.setPayErrorMessage(errorMsg);
                    order.setOrderState(OrderStateType.Failed);
                    localeMessage.append(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS.args(orgWarehouseCode));
                }
            }finally {
                if (OrderStateType.Failed.equals(order.getOrderState())) {
                    //主订单支付失败，子订单也支付失败
                    orderItem.setOrderState(OrderStateType.Failed);
                }
            }
        }
        if(!approvedTenant && (LogisticsTypeEnum.DropShipping.equals(logisticsType))){
            finalDeliveryFeeFromErp = distributorProductActivityPrice.getDistributorActivityFinalDeliveryFee();
            // 一口价不给仓库,但是仓库还是要塞默认值
            boolean aCase = false;
            ChannelTypeEnum channelType = order.getChannelType();
            carrierList = getCarrier(logisticsCompanyName, channelType, aCase, carrierList);
//            memberSuccessFlow(tenantId, order, productSkuCode, orderItemPrice, orderItem, localeMessage, logisticsType, totalQuantity, finalDeliveryFeeFromErp, productSku, productSkuPrice, memberPrice);
            // 仓库可配送国家判断
            List<Warehouse> deliverableWarehouse = new ArrayList<>();
            for(String warehouseCode : activityWarehouseCodeList){
                LambdaQueryWrapper<Warehouse> eq = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, productSku.getTenantId())
                                                                                      .eq(Warehouse::getWarehouseCode, warehouseCode)
                                                                                      .eq(Warehouse::getDelFlag, 0);
                List<Warehouse> warehouses = TenantHelper.ignore(() -> iWarehouseService.list(eq));
                if(CollUtil.isNotEmpty(warehouses)){
                    for (Warehouse warehouse : warehouses){
                        List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(warehouse.getWarehouseSystemCode()));
                        if(CollUtil.isNotEmpty(countryCodeList)){
                            if(countryCodeList.contains(countryCode)){
                                deliverableWarehouse.add(warehouse);
                            }
                        }
                    }
                }
            }
            // 判断可配送订单的仓库
            if(CollUtil.isEmpty(deliverableWarehouse)){
                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON();
                order.setPayErrorMessage(errorMsg);
                localeMessage.append(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                return orderItemPrice;
            }
            Warehouse warehouse = deliverableWarehouse.get(0);
            paramDTO.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
            if (CollUtil.isNotEmpty(carrierList)){
                paramDTO.setLogisticsCarrierCode(carrierList.get(0));
                paramDTO.setWarehouseCode(warehouse.getWarehouseCode());
            }else {
                paramDTO.setLogisticsCarrierCode("Fedex");
                paramDTO.setLogisticsCode("FEDHD");
                paramDTO.setWarehouseCode(warehouse.getWarehouseCode());
            }
        }
        if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
            paramDTO.setLogisticsCarrierCode(logisticsCompanyName);
            // 仓库可配送国家判断
            List<Warehouse> deliverableWarehouse = new ArrayList<>();
            for(String warehouseCode : stashList){
                LambdaQueryWrapper<Warehouse> eq = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, productSku.getTenantId())
                                                                                      .eq(Warehouse::getWarehouseCode, warehouseCode)
                                                                                      .eq(Warehouse::getDelFlag, 0);
                List<Warehouse> warehouses = TenantHelper.ignore(() -> iWarehouseService.list(eq));
                if(CollUtil.isNotEmpty(warehouses)){
                    for (Warehouse warehouse : warehouses){
                        List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(warehouse.getWarehouseSystemCode()));
                        if(CollUtil.isNotEmpty(countryCodeList)){
                            if(countryCodeList.contains(countryCode)){
                                deliverableWarehouse.add(warehouse);
                            }
                        }
                    }
                }
            }
            // 判断可配送订单的仓库
            if(CollUtil.isEmpty(deliverableWarehouse)){
                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON();
                order.setPayErrorMessage(errorMsg);
                localeMessage.append(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                return orderItemPrice;
            }
            Warehouse warehouse = deliverableWarehouse.get(0);
            paramDTO.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
            paramDTO.setWarehouseCode(warehouse.getWarehouseCode());
        }
        // 给orderItem和orderItemPrice赋值
        activityOrderPriceFlow(productSkuCode,orderItemPrice,orderItem,localeMessage,logisticsType,totalQuantity,finalDeliveryFeeFromErp,distributorProductActivityPrice);
        return orderItemPrice;
    }

    /**
     * 活动流程设置订单价格
     *
     * @param productSkuCode
     * @param orderItemPrice
     * @param orderItem
     * @param localeMessage
     * @param logisticsType
     * @param totalQuantity
     * @param finalDeliveryFeeFromErp
     * @param distributorProductActivityPrice
     */
    private void activityOrderPriceFlow( String productSkuCode, OrderItemPrice orderItemPrice,
                              OrderItem orderItem, LocaleMessage localeMessage, LogisticsTypeEnum logisticsType,
                              Integer totalQuantity, BigDecimal finalDeliveryFeeFromErp,
                               DistributorProductActivityPrice distributorProductActivityPrice) {
        orderItemPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeFromErp);
        orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
        BigDecimal originalPickUpPrice;
        BigDecimal originalDropShippingPrice;
        BigDecimal platformPickUpPrice;
        originalPickUpPrice = distributorProductActivityPrice.getDistributorActivityPickUpPrice();
        BigDecimal originalUnitPrice = distributorProductActivityPrice.getDistributorActivityUnitPrice();
        BigDecimal originalOperationFee = distributorProductActivityPrice.getDistributorActivityOperationFee();
        originalDropShippingPrice = originalUnitPrice.add(originalOperationFee).add(finalDeliveryFeeFromErp);
        platformPickUpPrice = distributorProductActivityPrice.getDistributorActivityPickUpPrice();
        BigDecimal platformDropShippingPrice = null;
        // 非折扣单价+操作费+派送费
        platformDropShippingPrice = originalUnitPrice.add(originalOperationFee).add(finalDeliveryFeeFromErp);

        BigDecimal platformFinalDeliveryFee = finalDeliveryFeeFromErp;

        BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
        // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
            } else {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
            }
        }
        // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
        // 原始应付总金额
        BigDecimal originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 原始实付总金额
        BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
        // 平台应付总金额
        BigDecimal platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
        // 平台应付总金额
        BigDecimal platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);

        // 供货商应得收入就是原始应付总金额 tag lty
        orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
        orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
        }
        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
            orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
        }
        orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);

        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
        orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        //平台实际支付单价（平台、分销商）
        orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        //平台实际支付总金额（平台、分销商）
        orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);

        orderItem.setPlatformRefundExecutableAmount(originalActualTotalAmount);
        orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
        orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
        orderItemPrice.setOriginalOperationFee(originalOperationFee);
        orderItemPrice.setPlatformOperationFee(originalOperationFee);

    }

    /**
     * 功能描述：重新计算旧订单金额
     *
     * @param order              秩序
     * @param orderItemPriceList 订购商品价格表
     * <AUTHOR>
     * @date 2024/08/22
     */
    @Deprecated
    public void recalculateOrderAmountOld(Orders order, List<OrderItemPrice> orderItemPriceList) {
        log.info("准备开始重新计算订单金额 子订单价格数据 = {}", JSONUtil.toJsonStr(orderItemPriceList));
        LogisticsTypeEnum logisticsType = order.getLogisticsType();
        BigDecimal originalTotalProductAmount = BigDecimal.ZERO;
        BigDecimal originalTotalOperationFee = BigDecimal.ZERO;
        BigDecimal originalTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal originalTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal originalTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal originalPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal originalPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
        BigDecimal originalRefundExecutableAmount = BigDecimal.ZERO;

        BigDecimal platformTotalProductAmount = BigDecimal.ZERO;
        BigDecimal platformTotalOperationFee = BigDecimal.ZERO;
        BigDecimal platformTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal platformTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal platformTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal platformPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal platformPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal platformActualTotalAmount = BigDecimal.ZERO;
        BigDecimal platformRefundExecutableAmount = BigDecimal.ZERO;
        // todo 要做order的数据埋点
        String countryCode = order.getCountryCode();
        Long siteId = null;
        siteId = iSiteCountryCurrencyService.getSiteIdByCountryCode(countryCode);
        for (OrderItemPrice orderItemPrice : orderItemPriceList) {
            Integer totalQuantity = orderItemPrice.getTotalQuantity();
            // 会员定价逻辑
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, orderItemPrice.getProductSkuCode())));
            Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));
            String tenantId = null;
            if (ObjectUtil.isNotEmpty(order.getTenantId())) {
                tenantId = order.getTenantId();
            } else {
                tenantId = LoginHelper.getTenantId();
            }
            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId(), siteId);
            BigDecimal originalPickUpPrice = null;
            BigDecimal originalDropShippingPrice = null;
            originalPickUpPrice = orderItemPrice.getOriginalPickUpPrice();

            originalDropShippingPrice = orderItemPrice.getOriginalDropShippingPrice();
            BigDecimal platformPickUpPrice = null;
            platformPickUpPrice = orderItemPrice.getPlatformPickUpPrice();

            BigDecimal platformDropShippingPrice = null;
            platformDropShippingPrice = orderItemPrice.getPlatformDropShippingPrice();

            BigDecimal platformFinalDeliveryFee = null;
            platformFinalDeliveryFee = orderItemPrice.getPlatformFinalDeliveryFee();
            BigDecimal originalUnitPrice = null;
            originalUnitPrice = orderItemPrice.getOriginalUnitPrice();
            BigDecimal originalOperationFee = null;
            originalOperationFee = orderItemPrice.getOriginalOperationFee();
            BigDecimal originalFinalDeliveryFee = null;
            originalFinalDeliveryFee = orderItemPrice.getOriginalFinalDeliveryFee();

            BigDecimal platformUnitPrice = null;
            platformUnitPrice = orderItemPrice.getPlatformUnitPrice();
//          会员计价
            BigDecimal platformOperationFee = null;
            platformOperationFee = orderItemPrice.getPlatformOperationFee();

            if (ObjectUtil.isNotEmpty(memberPrice)) {
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
                    originalPickUpPrice = memberPrice.getOriginalPickUpPrice();
                    if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                        orderItemPrice.setOriginalBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                    }
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
                    originalDropShippingPrice = memberPrice.getOriginalDropShippingPrice();
                    originalFinalDeliveryFee = memberPrice.getOriginalFinalDeliveryFee();
                    if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                        orderItemPrice.setOriginalBalanceUnitPrice(memberPrice.getOriginalDropShippingPrice());
                    }
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())) {
                    if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                        orderItemPrice.setPlatformBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
                    }
                    platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
                    if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                        orderItemPrice.setPlatformBalanceUnitPrice(memberPrice.getPlatformDropShippingPrice());
                    }
                    // tag lty
                    platformDropShippingPrice = memberPrice.getPlatformDropShippingPrice();
                    platformFinalDeliveryFee = memberPrice.getPlatformFinalDeliveryFee();
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice()) || ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
                    originalUnitPrice = memberPrice.getOriginalUnitPrice();

                    originalOperationFee = memberPrice.getOriginalOperationFee();
                    platformOperationFee = memberPrice.getPlatformOperationFee();
                }
            }
//            会员定价逻辑  OriginalBalanceUnitPrice  OriginalBalanceUnitPrice

            BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
            BigDecimal originalBalanceUnitPrice = orderItemPrice.getOriginalBalanceUnitPrice();

            BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
            BigDecimal platformBalanceUnitPrice = orderItemPrice.getPlatformBalanceUnitPrice();

            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);

                // 代发时，应付单价需要加上尾程派送费
                // originalBalanceUnitPrice = NumberUtil.add(originalBalanceUnitPrice, orderItemPrice.getOriginalFinalDeliveryFee());
                // platformBalanceUnitPrice = NumberUtil.add(platformBalanceUnitPrice, orderItemPrice.getPlatformFinalDeliveryFee());
            }

            originalTotalProductAmount = originalTotalProductAmount.add(NumberUtil.mul(originalUnitPrice, totalQuantity));
            originalTotalOperationFee = originalTotalOperationFee.add(NumberUtil.mul(originalOperationFee, totalQuantity));
            originalTotalFinalDeliveryFee = originalTotalFinalDeliveryFee.add(NumberUtil.mul(originalFinalDeliveryFee, totalQuantity));
            originalTotalPickUpPrice = originalTotalPickUpPrice.add(NumberUtil.mul(originalPickUpPrice, totalQuantity));
            originalTotalDropShippingPrice = originalTotalDropShippingPrice.add(NumberUtil.mul(originalDropShippingPrice, totalQuantity));

            originalPayableTotalAmount = originalPayableTotalAmount.add(NumberUtil.mul(originalPayableUnitPrice, totalQuantity));
            originalPrepaidTotalAmount = originalPrepaidTotalAmount.add(NumberUtil.mul(originalDepositUnitPrice, totalQuantity));
            originalActualTotalAmount = originalActualTotalAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
            originalRefundExecutableAmount = originalRefundExecutableAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));

            platformTotalProductAmount = platformTotalProductAmount.add(NumberUtil.mul(platformUnitPrice, totalQuantity));
            platformTotalOperationFee = platformTotalOperationFee.add(NumberUtil.mul(platformOperationFee, totalQuantity));
            platformTotalFinalDeliveryFee = platformTotalFinalDeliveryFee.add(NumberUtil.mul(platformFinalDeliveryFee, totalQuantity));
            platformTotalPickUpPrice = platformTotalPickUpPrice.add(NumberUtil.mul(platformPickUpPrice, totalQuantity));
            platformTotalDropShippingPrice = platformTotalDropShippingPrice.add(NumberUtil.mul(platformDropShippingPrice, totalQuantity));

            platformPayableTotalAmount = platformPayableTotalAmount.add(NumberUtil.mul(platformPayableUnitPrice, totalQuantity));
            platformPrepaidTotalAmount = platformPrepaidTotalAmount.add(NumberUtil.mul(platformDepositUnitPrice, totalQuantity));
            platformActualTotalAmount = platformActualTotalAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
            platformRefundExecutableAmount = platformRefundExecutableAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
        }

        order.setOriginalTotalProductAmount(originalTotalProductAmount);
        order.setOriginalTotalOperationFee(originalTotalOperationFee);
        order.setOriginalTotalFinalDeliveryFee(originalTotalFinalDeliveryFee);
        order.setOriginalTotalPickUpPrice(originalTotalPickUpPrice);
        order.setOriginalTotalDropShippingPrice(originalTotalDropShippingPrice);
        order.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        order.setOriginalPrepaidTotalAmount(originalPrepaidTotalAmount);
        order.setOriginalActualTotalAmount(originalActualTotalAmount);
        order.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);

        order.setPlatformTotalProductAmount(platformTotalProductAmount);
        order.setPlatformTotalOperationFee(platformTotalOperationFee);
        order.setPlatformTotalFinalDeliveryFee(platformTotalFinalDeliveryFee);

        order.setPlatformTotalPickUpPrice(platformTotalPickUpPrice);

        order.setPlatformTotalDropShippingPrice(platformTotalDropShippingPrice);
        order.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        order.setPlatformPrepaidTotalAmount(platformPrepaidTotalAmount);
        // 渠道订单,此处不赋予值的变更
        Integer orderSource = order.getOrderSource();
        ChannelTypeEnum channelType = order.getChannelType();
        // 数据清洗后基本上满足条件
        if(ObjectUtil.isNotEmpty(orderSource)&& ObjectUtil.isNotEmpty(channelType)){
            // 接口订单与openApi订单不需要再次对销售额金额进行重新计算,但后续可能会有其他逻辑此处保留
            if((OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderSource)||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource))){

            }else{
                order.setPlatformActualTotalAmount(platformActualTotalAmount);
            }

        }else{
            order.setPlatformActualTotalAmount(platformActualTotalAmount);
        }

        order.setPlatformRefundExecutableAmount(platformRefundExecutableAmount);
        log.info("重新计算后的订单金额 = {}", JSONUtil.toJsonStr(order));
    }
    /**
     * 主订单重新计算订单金额,包含会员价,内部循环,原因是单子目前都是单个的,不会有多个
     * 适用场景: 1. 支付前置检查 2. 临时单转换正式单 3. 分销商改变订单详情
     * 影响业务: Orders (要带channelType)
     * 必备参数: orders: logisticsType tenantId   List<OrderItemPrice>  totalQuantity,productSkuCode
     * 业务前言: orderItemPriceList 为子订单价格数据 由calculationOrderItemPrice 方法生成,两个方法需在同一作用域
     * 禁用业务: tiktok订单
     * 禁用Demo:
     * if (ChannelTypeEnum.TikTok.equals(order.getChannelType())) {
     * } else {
     * recalculateOrderAmount(order, orderItemPriceList);
     * }
     *
     * @param order
     * @param orderItemPriceList
     */
    public void recalculateOrderAmount(Orders order, List<OrderItemPrice> orderItemPriceList) {
        log.info("准备开始重新计算订单金额 子订单价格数据 = {}", JSONUtil.toJsonStr(orderItemPriceList));
        LogisticsTypeEnum logisticsType = order.getLogisticsType();
        BigDecimal originalTotalProductAmount = BigDecimal.ZERO;
        BigDecimal originalTotalOperationFee = BigDecimal.ZERO;
        BigDecimal originalTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal originalTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal originalTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal originalPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal originalPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
        BigDecimal originalRefundExecutableAmount = BigDecimal.ZERO;

        BigDecimal platformTotalProductAmount = BigDecimal.ZERO;
        BigDecimal platformTotalOperationFee = BigDecimal.ZERO;
        BigDecimal platformTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal platformTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal platformTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal platformPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal platformPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal platformActualTotalAmount = BigDecimal.ZERO;
        BigDecimal platformRefundExecutableAmount = BigDecimal.ZERO;
        Long siteId = order.getSiteId();
        // 获取orderItem
        OrderItem orderItem = TenantHelper.ignore(() -> iOrderItemService.getOne(new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderId, order.getId())));
        Boolean isActivity = false;
        if(null != orderItem && null != orderItem.getActivityCode()){
            isActivity = true;
        }
        // 分销商id
        String tenantId = order.getTenantId();
        if (!ObjectUtil.isNotEmpty(tenantId)) {
            tenantId = LoginHelper.getTenantId();
        }

        for (OrderItemPrice orderItemPrice : orderItemPriceList) {
            Integer totalQuantity = orderItemPrice.getTotalQuantity();
            // 会员定价逻辑
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, orderItemPrice.getProductSkuCode())));
            String supTenantId = productSku.getTenantId();
            String productSkuCode = productSku.getProductSkuCode();
            Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));


            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId(), siteId);
            BigDecimal originalPickUpPrice = null;
            BigDecimal originalDropShippingPrice = null;
            originalPickUpPrice = orderItemPrice.getOriginalPickUpPrice();
            //
            originalDropShippingPrice = orderItemPrice.getOriginalDropShippingPrice();
            BigDecimal platformPickUpPrice = null;
            platformPickUpPrice = orderItemPrice.getPlatformPickUpPrice();

            BigDecimal platformDropShippingPrice = null;
            platformDropShippingPrice = orderItemPrice.getPlatformDropShippingPrice();

            BigDecimal platformFinalDeliveryFee = null;
            // 此处需要调用erp了,如果下面的erp调用的结果不一致,则需要重新计算
            platformFinalDeliveryFee = orderItemPrice.getPlatformFinalDeliveryFee();
            BigDecimal originalUnitPrice = null;
            originalUnitPrice = orderItemPrice.getOriginalUnitPrice();
            BigDecimal originalOperationFee = null;
            originalOperationFee = orderItemPrice.getOriginalOperationFee();
            BigDecimal originalFinalDeliveryFee = null;
            // 这里实际上是从erp获取的,此处已经做好了erp配送费的金额计算了
            originalFinalDeliveryFee = orderItemPrice.getOriginalFinalDeliveryFee();
            BigDecimal platformUnitPrice = null;
            BigDecimal platformOperationFee = null;
            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);

            BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
            BigDecimal originalBalanceUnitPrice = orderItemPrice.getOriginalBalanceUnitPrice();

            BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
            BigDecimal platformBalanceUnitPrice = orderItemPrice.getPlatformBalanceUnitPrice();
            if(ObjectUtil.isEmpty(originalFinalDeliveryFee)){
                originalTotalProductAmount = originalTotalProductAmount.add(NumberUtil.mul(originalUnitPrice, totalQuantity));
                originalTotalOperationFee = originalTotalOperationFee.add(NumberUtil.mul(originalOperationFee, totalQuantity));

                originalTotalFinalDeliveryFee = null;
                originalTotalPickUpPrice = originalTotalPickUpPrice.add(NumberUtil.mul(originalPickUpPrice, totalQuantity));
                originalTotalDropShippingPrice = null;
                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                    if(ObjectUtil.isNotEmpty(originalDropShippingPrice)){
                        originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                    }else {
                        originalPayableUnitPrice = null;
                    }
                    if(ObjectUtil.isNotEmpty(platformDropShippingPrice)){
                        platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
                    }else {
                        platformPayableUnitPrice = null;
                    }

                }
                if(ObjectUtil.isNotEmpty(originalPayableUnitPrice)){
                    originalPayableTotalAmount = originalPayableTotalAmount.add(NumberUtil.mul(originalPayableUnitPrice, totalQuantity));
                }else {
                    originalPayableTotalAmount = null;
                }
                // zero
                if (ObjectUtil.isNotEmpty(originalDepositUnitPrice)){
                    originalPrepaidTotalAmount = originalPrepaidTotalAmount.add(NumberUtil.mul(originalDepositUnitPrice, totalQuantity));
                }else {
                    originalPrepaidTotalAmount = null;
                }

                // 如果是自提对应的就是自提价 如果是代发对应的就是代发价
                if(ObjectUtil.isNotEmpty(originalBalanceUnitPrice)){
                    originalActualTotalAmount = originalActualTotalAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
                    originalRefundExecutableAmount = originalRefundExecutableAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
                }else {
                    originalActualTotalAmount = null;
                    originalRefundExecutableAmount = null;
                }


                platformTotalProductAmount = platformTotalProductAmount.add(NumberUtil.mul(platformUnitPrice, totalQuantity));
                platformTotalOperationFee = platformTotalOperationFee.add(NumberUtil.mul(platformOperationFee, totalQuantity));

                platformTotalFinalDeliveryFee = null;

                platformTotalPickUpPrice = platformTotalPickUpPrice.add(NumberUtil.mul(platformPickUpPrice, totalQuantity));
                platformTotalDropShippingPrice = null;

                if(ObjectUtil.isNotEmpty(platformPayableUnitPrice)){
                    platformPayableTotalAmount = platformPayableTotalAmount.add(NumberUtil.mul(platformPayableUnitPrice, totalQuantity));
                }else {
                    platformPayableTotalAmount = null;
                }

                platformPrepaidTotalAmount = platformPrepaidTotalAmount.add(NumberUtil.mul(platformDepositUnitPrice, totalQuantity));
                if(ObjectUtil.isNotEmpty(platformBalanceUnitPrice)){
                    platformActualTotalAmount = platformActualTotalAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
                    platformRefundExecutableAmount = platformRefundExecutableAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
                }else {
                    platformActualTotalAmount = null;
                    platformRefundExecutableAmount = null;
                }

            }else{
                platformUnitPrice = orderItemPrice.getPlatformUnitPrice();
                platformDropShippingPrice = platformPickUpPrice.add(platformFinalDeliveryFee);
                platformOperationFee = orderItemPrice.getPlatformOperationFee();
                // 如果是分销商拿到会员等级的运费折扣,计算价格接口出异常,订单增加异常种类,相关计算的价格置为null
                BigDecimal finalDeliveryFeeFromErp;
                // 这里的费用已经算过会员价了
                finalDeliveryFeeFromErp = originalFinalDeliveryFee;
                // 活动订单不走会员价
                if (ObjectUtil.isNotEmpty(memberPrice)&&(ObjectUtil.isNotEmpty(memberPrice.getOriginalUnitPrice())&&ObjectUtil.isNotEmpty(memberPrice.getOriginalOperationFee()) && !isActivity)) {
                    if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
                        originalPickUpPrice = memberPrice.getOriginalPickUpPrice();
                        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                            orderItemPrice.setOriginalBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
                            orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                        }
                    }

                    // 会员派送费的折扣逻辑
                    BigDecimal unitPrice = memberPrice.getOriginalUnitPrice();
                    BigDecimal operationFee = memberPrice.getOriginalOperationFee();

                    BigDecimal memberDropShippingPrice = unitPrice.add(operationFee).add(finalDeliveryFeeFromErp);
                    // 这里的价格为null,说明走的是默认的单价和操作费
                    if (ObjectUtil.isNotEmpty(unitPrice) && ObjectUtil.isNotEmpty(operationFee)) {
                        originalDropShippingPrice = memberDropShippingPrice;

                        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                            orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                            orderItemPrice.setOriginalBalanceUnitPrice(originalDropShippingPrice);
                        }
                    }
                    if (ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())) {
                        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                            orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                            orderItemPrice.setPlatformBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
                        }
                        platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                    }
                    if (ObjectUtil.isNotEmpty(memberDropShippingPrice)) {
                        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                            orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                            // 这个既有代发也有自提,价格统一放这里
                            orderItemPrice.setPlatformBalanceUnitPrice(memberDropShippingPrice);
                        }
                        // tag lty
                        platformDropShippingPrice = memberDropShippingPrice;
                        platformFinalDeliveryFee = finalDeliveryFeeFromErp;
                    }
                    if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
                        originalUnitPrice = memberPrice.getOriginalUnitPrice();

                        originalOperationFee = memberPrice.getOriginalOperationFee();
                        platformOperationFee = memberPrice.getPlatformOperationFee();
                    }
                }
//            会员定价逻辑  OriginalBalanceUnitPrice  OriginalBalanceUnitPrice
                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);

                    // 代发时，应付单价需要加上尾程派送费
                    // originalBalanceUnitPrice = NumberUtil.add(originalBalanceUnitPrice, orderItemPrice.getOriginalFinalDeliveryFee());
                    // platformBalanceUnitPrice = NumberUtil.add(platformBalanceUnitPrice, orderItemPrice.getPlatformFinalDeliveryFee());
                }

                originalTotalProductAmount = originalTotalProductAmount.add(NumberUtil.mul(originalUnitPrice, totalQuantity));
                originalTotalOperationFee = originalTotalOperationFee.add(NumberUtil.mul(originalOperationFee, totalQuantity));

                originalTotalFinalDeliveryFee = originalTotalFinalDeliveryFee.add(NumberUtil.mul(finalDeliveryFeeFromErp, totalQuantity));
                originalTotalPickUpPrice = originalTotalPickUpPrice.add(NumberUtil.mul(originalPickUpPrice, totalQuantity));
                originalTotalDropShippingPrice = originalTotalDropShippingPrice.add(NumberUtil.mul(originalDropShippingPrice, totalQuantity));

                originalPayableTotalAmount = originalPayableTotalAmount.add(NumberUtil.mul(originalPayableUnitPrice, totalQuantity));
                originalPrepaidTotalAmount = originalPrepaidTotalAmount.add(NumberUtil.mul(originalDepositUnitPrice, totalQuantity));
                originalActualTotalAmount = originalActualTotalAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
                originalRefundExecutableAmount = originalRefundExecutableAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));

                platformTotalProductAmount = platformTotalProductAmount.add(NumberUtil.mul(platformUnitPrice, totalQuantity));
                platformTotalOperationFee = platformTotalOperationFee.add(NumberUtil.mul(platformOperationFee, totalQuantity));
                platformTotalFinalDeliveryFee = platformTotalFinalDeliveryFee.add(NumberUtil.mul(platformFinalDeliveryFee, totalQuantity));
                platformTotalPickUpPrice = platformTotalPickUpPrice.add(NumberUtil.mul(platformPickUpPrice, totalQuantity));
                platformTotalDropShippingPrice = platformTotalDropShippingPrice.add(NumberUtil.mul(platformDropShippingPrice, totalQuantity));

                platformPayableTotalAmount = platformPayableTotalAmount.add(NumberUtil.mul(platformPayableUnitPrice, totalQuantity));
                platformPrepaidTotalAmount = platformPrepaidTotalAmount.add(NumberUtil.mul(platformDepositUnitPrice, totalQuantity));
                platformActualTotalAmount = platformActualTotalAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
                platformRefundExecutableAmount = platformRefundExecutableAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
            }
            // 此处时order 有尾程派送费
        }

        order.setOriginalTotalProductAmount(originalTotalProductAmount);
        order.setOriginalTotalOperationFee(originalTotalOperationFee);
        order.setOriginalTotalFinalDeliveryFee(originalTotalFinalDeliveryFee);
        order.setOriginalTotalPickUpPrice(originalTotalPickUpPrice);
        order.setOriginalTotalDropShippingPrice(originalTotalDropShippingPrice);
        order.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        order.setOriginalPrepaidTotalAmount(originalPrepaidTotalAmount);
        order.setOriginalActualTotalAmount(originalActualTotalAmount);
        order.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);

        order.setPlatformTotalProductAmount(platformTotalProductAmount);
        order.setPlatformTotalOperationFee(platformTotalOperationFee);
        order.setPlatformTotalFinalDeliveryFee(platformTotalFinalDeliveryFee);

        order.setPlatformTotalPickUpPrice(platformTotalPickUpPrice);

        order.setPlatformTotalDropShippingPrice(platformTotalDropShippingPrice);
        order.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        order.setPlatformPrepaidTotalAmount(platformPrepaidTotalAmount);
        // 渠道订单,此处不赋予值的变更
        Integer orderSource = order.getOrderSource();
        ChannelTypeEnum channelType = order.getChannelType();
        // 数据清洗后基本上满足条件
        if(ObjectUtil.isNotEmpty(orderSource)&& ObjectUtil.isNotEmpty(channelType)){
            // 接口订单与openApi订单不需要再次对销售额金额进行重新计算,但后续可能会有其他逻辑此处保留
            if((OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderSource)||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource))){

            }else{
                order.setPlatformActualTotalAmount(platformActualTotalAmount);
            }

        }else{
            order.setPlatformActualTotalAmount(platformActualTotalAmount);
        }

        order.setPlatformRefundExecutableAmount(originalRefundExecutableAmount);
        log.info("重新计算后的订单金额 = {}", JSONUtil.toJsonStr(order));
    }

    /**
     * 功能描述：
     * 功能描述：收到原始最终运费后,4位精度
     *
     * @param ruleCustomizerTenantId  规则定制器租户id
     * @param ruleFollowerTenantId    规则跟随者租户id
     * @param finalDeliveryFeeFromErp erp原始最终交付费
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/09/06
     */
    public BigDecimal getOriginalFinalDeliveryFeeAfterDiscount(String ruleCustomizerTenantId, String ruleFollowerTenantId, BigDecimal finalDeliveryFeeFromErp) {
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(ruleFollowerTenantId, 1);
        if (approvedTenant){
            // 折扣系数实际要落在等级,所以要先找等级
            LambdaQueryWrapper<MemberRuleRelation> wrapper = new LambdaQueryWrapper<MemberRuleRelation>()
                .eq(MemberRuleRelation::getRuleCustomizerTenantId, ruleCustomizerTenantId)
                .eq(MemberRuleRelation::getRuleFollowerTenantId, ruleFollowerTenantId)
                .eq(MemberRuleRelation::getDelFlag, 0);
            MemberRuleRelation one = iMemberRuleRelationService.getOne(wrapper);
            BigDecimal sum =BigDecimal.ZERO;
            // 有会员价
            if(ObjectUtil.isNotEmpty(one)){
                Long levelId = one.getLevelId();
                MemberLevel memberLevel = TenantHelper.ignore(()->iMemberLevelService.getById(levelId));
                Integer status = memberLevel.getStatus();
                if(status==1){
                    sum = finalDeliveryFeeFromErp.multiply(BigDecimal.valueOf(1.13)).setScale(2, RoundingMode.HALF_UP);
                    log.info(StrUtil.format("[获取ERP尾程派送费],计算会员尾程派送费,开通测算,开通会员，但是禁用,ERP返回派送费：{}，折扣系数：{}",finalDeliveryFeeFromErp,13 ));
                }else {
                    Long dictCode = memberLevel.getDictCode();
                    //获取折扣系数
                    BigDecimal discountFactor = memberSupport.getDiscountFactor(dictCode);
                    log.info(StrUtil.format("[获取ERP尾程派送费],计算会员尾程派送费，ERP返回派送费：{}，折扣系数：{}",finalDeliveryFeeFromErp,discountFactor ));
                    if(ObjectUtil.isEmpty(discountFactor)){
                        throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费],计算会员尾程派送费异常,未获取到会员尾程派送费折扣系数"));
                    }
                    discountFactor = discountFactor.divide(new BigDecimal("100"),2, RoundingMode.HALF_UP);
                    sum = finalDeliveryFeeFromErp.add(finalDeliveryFeeFromErp.multiply(discountFactor)).setScale(2, RoundingMode.HALF_UP);
                }
            }else{
                log.info(StrUtil.format("[获取ERP尾程派送费],计算尾程派送费,未开通测算ERP返回派送费：{}，折扣系数：{}",finalDeliveryFeeFromErp,13 ));
                sum = finalDeliveryFeeFromErp.multiply(BigDecimal.valueOf(1.13)).setScale(2, RoundingMode.HALF_UP);
            }
            return sum;
        }else {
            return finalDeliveryFeeFromErp;
        }


    }


//    /**
//     * 功能描述：一口价
//     *
//     * @param ruleCustomizerTenantId  规则定制器租户id
//     * @param ruleFollowerTenantId    规则跟随者租户id
//     * @param finalDeliveryFeeFromErp erp最终交付费用
//     * @param isDistributionCalculate 是否计算分布
//     * @return {@link BigDecimal }
//     * <AUTHOR>
//     * @date 2024/09/06
//     */
//    public BigDecimal getOriginalFinalDeliveryFeeAfterDiscount(String ruleCustomizerTenantId, String ruleFollowerTenantId, BigDecimal finalDeliveryFeeFromErp,
//                                                               boolean isDistributionCalculate) {
//        BigDecimal sum =BigDecimal.ZERO;
//        sum = finalDeliveryFeeFromErp;
//
//
//        return sum;
//
//    }

    /**
     * 功能描述：
     * 功能描述：从erp获取原始最终交付费用 该接口如果没有返回价格,则尾程派送费异常
     * example : null 未获取到费用,可能是接口异常 或是 库存不足
     *
     * @param stashList      仓库清单
     * @param zipCode        邮政编码
     * @param productSkuCode 产品sku代码
     * @param dTenantId      分销商id
     * @param sTenantId
     * @param countryCode
     * @param postcode
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/07/30
     */
    public DeliveryFeeByErpResponse getDeliveryFeeFromErp(List<String> stashList, String zipCode, String productSkuCode, List<String> logisticsCode,
                                                          String dTenantId, String sTenantId, String countryCode,
                                                          String postcode) throws DeliveryFeeException {
        List<DeliveryFeeByErpResponse> deliveryFeeByErp ;
        DeliveryFeeByErpResponse deliveryFeeByErpResponse = null;
        DeliveryFeeByErpRequest deliveryFeeByErpRequest = getDeliveryFeeErpRequest(stashList,zipCode,productSkuCode,logisticsCode, dTenantId, sTenantId,countryCode,postcode);
        // 测算就把下面的放开
        try{
            deliveryFeeByErp = deliveryFeeUtils.getDeliveryFeeByErp(Collections.singletonList(deliveryFeeByErpRequest),sTenantId);
            deliveryFeeByErpResponse = deliveryFeeByErp.get(0);
//            throw new DeliveryFeeException("获取erp尾程派送费失败");
        }catch (Exception e){
            throw new DeliveryFeeException("获取erp尾程派送费失败");
        }
        return deliveryFeeByErpResponse;

    }
//    public DeliveryFeeByErpResponse getDeliveryFeeFromErp(List<String> stashList, String zipCode, String productSkuCode, List<String> logisticsCode,
//                                                          String dTenantId, String sTenantId) {
//        List<DeliveryFeeByErpResponse> deliveryFeeByErp ;
//        DeliveryFeeByErpResponse deliveryFeeByErpResponse = null;
//        DeliveryFeeByErpRequest deliveryFeeByErpRequest = getDeliveryFeeErpRequest(stashList,zipCode,productSkuCode,logisticsCode, dTenantId, sTenantId);
//        // 测算就把下面的放开
//        try{
//            deliveryFeeByErp = deliveryFeeUtils.getDeliveryFeeByErp(Collections.singletonList(deliveryFeeByErpRequest),dTenantId);
//            deliveryFeeByErpResponse = deliveryFeeByErp.get(0);
//        }catch (Exception e){
////            int i = TenantHelper.ignore(()->Integer.parseInt(sysConfigService.selectConfigByKey("IS_DeliveryFee_ABLE")));
////            if(i==1){
////                log.error("获取erp尾程派送费失败,尝试使用备用接口");
////                //todo theo
////                List<DeliveryFeeByErpResponse> deliveryFeeByErpByError = deliveryFeeSupport.getDeliveryFeeByErpByError(Collections.singletonList(deliveryFeeByErpRequest),null);
////                return deliveryFeeByErpByError.get(0);
////            }else {
////                throw new RuntimeException("获取erp尾程派送费失败");
////            }
//            throw new RuntimeException("获取erp尾程派送费失败");
//        }
////        List<String> carrierCodes = deliveryFeeByErpRequest.getCarrierCodes();
////        String carrier =null;
////        if(CollUtil.isNotEmpty(carrierCodes)){
////            carrier = carrierCodes.get(0);
////        }
//        // 把这个注释 todo theo
////        List<DeliveryFeeByErpResponse> deliveryFeeByErpByError = deliveryFeeSupport.getDeliveryFeeByErpByError(Collections.singletonList(deliveryFeeByErpRequest), carrier);
//        return deliveryFeeByErpResponse;
//
//    }
    @NotNull
    private DeliveryFeeByErpRequest getDeliveryFeeErpRequest(List<String> stashList, String postalCode, String productSkuCode, List<String> carrierCodes,
                                                             String dTenantId, String sTenantId, String countryCode,
                                                             String postcode) {

        ProductSku one = TenantHelper.ignore(()->iProductSkuService.getOne(Wrappers.<ProductSku>lambdaQuery()
                                                                                   .eq(ProductSku::getProductSkuCode, productSkuCode)
                                                                                   .eq(ProductSku::getDelFlag, 0)));
        DeliveryFeeByErpRequest deliveryFeeByErpRequest = new DeliveryFeeByErpRequest();

        String channelFlag = TenantHelper.ignore(()->productSkuStockMapper.getChannelFlag(dTenantId));
        deliveryFeeByErpRequest.setChannelFlag(channelFlag);
        deliveryFeeByErpRequest.setDistributorTenantId(dTenantId);
        deliveryFeeByErpRequest.setSupplierTenantId(sTenantId);
        deliveryFeeByErpRequest.setOrgWarehouseCodeList(stashList);
        deliveryFeeByErpRequest.setPostcode(postalCode);// us 必要
        deliveryFeeByErpRequest.setCountryCode(countryCode);
        DeliveryFeeByErpRequest.ProductItem productItem = new DeliveryFeeByErpRequest.ProductItem();
        productItem.setErpSku(one.getSku());
        productItem.setQuantity(1);

        deliveryFeeByErpRequest.setSkuList(Collections.singletonList(productItem));
        deliveryFeeByErpRequest.setCarrierCodes(carrierCodes);
        return deliveryFeeByErpRequest;
    }
    @Deprecated
    private DeliveryFeeByErpRequest getDeliveryFeeByErpRequest(List<ProductSkuStock> productSkuStocks, String postalCode, String productSkuCode, String logisticsCode) {
        List<String> collect = productSkuStocks.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                               .collect(Collectors.toList());
        List<String> codes = productSkuStockMapper.getWarehouseCode(collect);
        ProductSku one = iProductSkuService.getOne(Wrappers.<ProductSku>lambdaQuery()
                                                           .eq(ProductSku::getProductSkuCode, productSkuCode)
                                                           .eq(ProductSku::getDelFlag, 0));
        ProductSkuStock productSkuStock = productSkuStocks.get(0);
        DeliveryFeeByErpRequest deliveryFeeByErpRequest = new DeliveryFeeByErpRequest();
        // hengjian
        String tenantId = productSkuStock.getTenantId();
        String channelFlag = TenantHelper.ignore(()->productSkuStockMapper.getChannelFlag(tenantId));
        deliveryFeeByErpRequest.setChannelFlag(channelFlag);
        deliveryFeeByErpRequest.setOrgWarehouseCodeList(codes);
        deliveryFeeByErpRequest.setPostcode(postalCode);
        DeliveryFeeByErpRequest.ProductItem productItem = new DeliveryFeeByErpRequest.ProductItem();
        productItem.setErpSku(one.getSku());
        productItem.setQuantity(1);

        deliveryFeeByErpRequest.setSkuList(Collections.singletonList(productItem));
        deliveryFeeByErpRequest.setCarrierCodes(Collections.singletonList(logisticsCode));
        return deliveryFeeByErpRequest;
    }
    /**
     * 通用价格计算 影响业务: OrderItemPrice OrderItem 中的金额相关计算
     * 该方法会调用erp价格
     * 计算子订单价格
     * 计算好后会直接修改入参的DTO中的orderItem，并且会set一个新的OrderItemPrice
     * 此方法不做任何save操作，所有实体类在外部再自行保存
     * 使用该方法 要注意,该方法中的异常保存在LocaleMessage中,需要在外部处理
     * 该方法只处理金额计算
     * 根据业务可传参数:activityCode
     *
     * @param paramDTO             订单价格计算参数必填:logisticsType OrderItem [orderItemNo,productSkuCode,totalQuantity]
     * @param tenantId             分销商id
     * @param zipCode
     * @param order
     * @param flowEnum
     * @param logisticsCompanyName
     * @return {@link LocaleMessage } 通过hasData判断是否有错误
     */
    public LocaleMessage calculationOrderItemPrice(OrderPriceCalculateDTO paramDTO, String tenantId, String zipCode, List<String> stashList, Orders order, OrderFlowEnum flowEnum, String logisticsCompanyName) {

        LocaleMessage localeMessage = new LocaleMessage();
        String country = paramDTO.getCountry();
        LogisticsTypeEnum logisticsType = paramDTO.getLogisticsType();
        ChannelTypeEnum channelTypeEnum = paramDTO.getChannelTypeEnum();
        OrderItem orderItem = paramDTO.getOrderItem();
        String orderItemNo = orderItem.getOrderItemNo();
        String productSkuCode = orderItem.getProductSkuCode();
        Integer totalQuantity = orderItem.getTotalQuantity();
        String activityCode = paramDTO.getActivityCode();
        String orderNo = order.getOrderNo();

        SupportedLogisticsEnum supportedLogistics = iProductService.querySupportedLogistics(productSkuCode);

        // 当前订单需要自提，但商品仅支持代发，返回提示
        if (LogisticsTypeEnum.PickUp.equals(logisticsType) && SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)) {
            localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_DROP_SHIPPING.args(productSkuCode));
        }

        // 当前订单需要代发，但商品仅支持自提，返回提示
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType) && SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
            localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
        }

        //如果已经配置了新的定价方式，则从productSkuPrice中取价格
        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItemNo);
        if (orderItemPrice == null) {
            orderItemPrice = new OrderItemPrice();
        }

        orderItemPrice.setOrderItemNo(orderItemNo);
        orderItemPrice.setProductSkuCode(productSkuCode);
        orderItemPrice.setLogisticsType(logisticsType);
        orderItemPrice.setTotalQuantity(totalQuantity);

        if (StrUtil.isNotBlank(activityCode)) {  // 存在活动编号，价格需要特殊处理
            orderItemPrice =  activityFlow(paramDTO,activityCode, productSkuCode, orderItemPrice, logisticsType, totalQuantity, orderItem,tenantId,logisticsCompanyName,order,localeMessage,zipCode, stashList);
        } else {  // 不存在活动编号，走正常价格
            // 会员价 改造  todo 检查欧洲站点改造影响
            orderItemPrice = getOrderItemPrice(paramDTO, tenantId, zipCode, stashList, order, logisticsCompanyName, productSkuCode, orderItemPrice, orderItem, localeMessage, logisticsType, totalQuantity, order.getSiteId());

        }
        paramDTO.setOrderItemPrice(orderItemPrice);
        //自提单使用客户指定的仓库
        if (ObjectUtil.equal(order.getLogisticsType().name(),LogisticsTypeEnum.PickUp.name())){
            if(null != order.getExceptionCode() && order.getExceptionCode().equals(OrderExceptionEnum.warehouse_mapping_exception.getValue())){
                return localeMessage;
            }
            OrderItemProductSku byOrderItemId = orderItemProductSkuService.getByOrderItemId(orderItem.getId());
            if (ObjectUtil.isNotNull(byOrderItemId)){
                String warehouseSystemCode = byOrderItemId.getWarehouseSystemCode();
                // 仓库国家匹配不上，不报错
                if (null != order.getPayErrorMessage() && StrUtil.isEmpty(warehouseSystemCode) && !LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON().toString().equals(order.getPayErrorMessage().toString())){
                    throw new RuntimeException("自提订单业务处理异常,OrderItemProductSku仓库系统编码为空");
                }
                paramDTO.setWarehouseSystemCode(warehouseSystemCode);
            }
        }
        return localeMessage;
    }

    @org.jetbrains.annotations.NotNull
    private OrderItemPrice getOrderItemPrice(OrderPriceCalculateDTO paramDTO, String tenantId, String zipCode,
                                             List<String> stashList, Orders order, String logisticsCompanyName,
                                             String productSkuCode, OrderItemPrice orderItemPrice, OrderItem orderItem,
                                             LocaleMessage localeMessage, LogisticsTypeEnum logisticsType,
                                             Integer totalQuantity, Long siteId) {

        String countryCode = order.getCountryCode();

        // 增加站点维度zsmall.siteInformation.aScene
        if(ObjectUtil.isEmpty(siteId)){
            localeMessage.append(ZSMallStatusCodeEnum.SITE_INFORMATION_A_SCENE.args());
        }
        ProductSkuPrice productSkuPrice = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,siteId));
        ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getById(productSkuPrice.getProductSkuId()));

        Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));
        log.info("计算子订单价格 productSkuCode = {} productSkuPrice = {}", productSkuCode, JSONUtil.toJsonStr(productSkuPrice));
        // 结算会以product_sku_price
        orderItemPrice = getOrderItemPriceByChannel(productSkuPrice, orderItem, orderItemPrice);
        // 会员入口 此处orderItem
        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId(), siteId);
        // erp尾程派送费
        BigDecimal finalDeliveryFeeFromErp = null;
        Boolean isErp = true;
        List<String> list = new ArrayList<>();
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(tenantId, 1);
        LogisticsTypeEnum type = paramDTO.getLogisticsType();
        if(CollUtil.isEmpty(stashList)){
            // 仓库清单为空,则不走erp,测算异常 订单应为测算异常
            if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                localeMessage.append(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                order.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
            }
            order.setPlatformTotalFinalDeliveryFee(null);
            order.setOriginalTotalFinalDeliveryFee(null);
            isErp = false;
            paramDTO.setWarehouseSystemCode(null);
        }else {
            // 如果是测算,进测算逻辑 调用测算接口,接口出现问题则尾程异常,如果正常则继续走
            if (approvedTenant && (!LogisticsTypeEnum.PickUp.equals(type))) {
                ChannelTypeEnum channelType = order.getChannelType();
                boolean aCase = false;
                list = getCarrier(logisticsCompanyName, channelType, aCase, list);
                DeliveryFeeByErpResponse deliveryFeeFromErp = null;
                String orgWarehouseCode = null;
                try{
                    // 仓库可配送国家判断
                    List<Warehouse> deliverableWarehouse = new ArrayList<>();
                    List<String> warehouseCodeList = new ArrayList<>();
                    for(String warehouseCode : stashList){
                        LambdaQueryWrapper<Warehouse> eq = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, productSku.getTenantId())
                                                                                              .eq(Warehouse::getWarehouseCode, warehouseCode)
                                                                                              .eq(Warehouse::getDelFlag, 0);
                        List<Warehouse> warehouses = TenantHelper.ignore(() -> iWarehouseService.list(eq));
                        if(CollUtil.isNotEmpty(warehouses)){
                            for (Warehouse warehouse : warehouses){
                                List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(warehouse.getWarehouseSystemCode()));
                                if(CollUtil.isNotEmpty(countryCodeList)){
                                    if(countryCodeList.contains(countryCode)){
                                        deliverableWarehouse.add(warehouse);
                                        warehouseCodeList.add(warehouseCode);
                                    }
                                }
                            }
                        }
                    }
                    // 判断可配送订单的仓库
                    if(CollUtil.isEmpty(deliverableWarehouse)){
                        JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON();
                        order.setPayErrorMessage(errorMsg);
                        order.setExceptionCode(OrderExceptionEnum.measurement_anomaly.getValue());
                        localeMessage.append(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                        order.setPlatformTotalFinalDeliveryFee(null);
                        order.setOriginalTotalFinalDeliveryFee(null);
                        return orderItemPrice;
                    }else {
                        //判断上次测算时间超过2小时的话，重新测算
                        if(order.getCalculationTime()==null || DateUtil.between(order.getCalculationTime(),new Date(), DateUnit.HOUR)>2 ||order.getOrderSource()!=4){
                            deliveryFeeFromErp = getDeliveryFeeFromErp(warehouseCodeList, zipCode, productSkuCode, list, tenantId, productSku.getTenantId(), countryCode, zipCode);
                            //如果测算成功,立马更新订单的测算时间
                            ordersService.update(new LambdaUpdateWrapper<Orders>()
                                .set(Orders::getCalculationTime, new Date())
                                .eq(Orders::getId, order.getId()));

                            finalDeliveryFeeFromErp = deliveryFeeFromErp.getShippingFee();
                            orgWarehouseCode = deliveryFeeFromErp.getOrgWarehouseCode();
                            String carrierCode = deliveryFeeFromErp.getCarrierCode();
                            String logisticsCode = deliveryFeeFromErp.getLogisticCode();
                            if (ObjectUtil.isEmpty(finalDeliveryFeeFromErp)) {
                                throw new DeliveryFeeException("erp接口异常");
                            }
                            LambdaQueryWrapper<Warehouse> eq = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, productSku.getTenantId())
                                                                                                  .eq(Warehouse::getWarehouseCode, orgWarehouseCode)
                                                                                                  .eq(Warehouse::getDelFlag, 0);
                            List<Warehouse> warehouses = TenantHelper.ignore(() -> iWarehouseService.list(eq));
                            if (CollUtil.isEmpty(warehouses)||warehouses.size()>1){
                                throw new StockException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS.args(orgWarehouseCode));
                            }
                            Warehouse warehouse = warehouses.get(0);

                            paramDTO.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                            paramDTO.setLogisticsCarrierCode(carrierCode);
                            paramDTO.setLogisticsCode(logisticsCode);
                            paramDTO.setWarehouseCode(orgWarehouseCode);
                            //更新发货仓库
                            orderItemProductSkuService
                                .update(new LambdaUpdateWrapper<OrderItemProductSku>()
                                    .set(OrderItemProductSku::getSpecifyWarehouse,warehouse.getWarehouseSystemCode())
                                    .set(OrderItemProductSku::getWarehouseSystemCode,warehouse.getWarehouseSystemCode())
                                    .eq(OrderItemProductSku::getOrderItemNo,orderItem.getOrderItemNo()));

                        }
                    }
                }catch (DeliveryFeeException e){
                    isErp = false;
                    if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                        order.setExceptionCode(OrderExceptionEnum.final_delivery_fee_exception.getValue());
                        try {
                            throw new DeliveryFeeException(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION.args(productSkuCode));
                        } catch (DeliveryFeeException ex) {
                            LocaleMessage localeMessage2 = ex.getLocaleMessage();
                            JSONObject errorMsg = localeMessage2.toJSON();
                            order.setPayErrorMessage(errorMsg);
                            order.setOrderState(OrderStateType.Failed);
                            localeMessage.append(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION.args(productSkuCode));
                        }
                    }
                }catch (StockException e){
                    isErp = false;
                    if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                        order.setExceptionCode(OrderExceptionEnum.stock_same_warehouse_code_exists.getValue());
                        LocaleMessage localeMessage2 = e.getLocaleMessage();
                        JSONObject errorMsg = localeMessage2.toJSON();
                        order.setPayErrorMessage(errorMsg);
                        order.setOrderState(OrderStateType.Failed);
                        localeMessage.append(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS.args(orgWarehouseCode));
                    }
                }finally {
                    if (OrderStateType.Failed.equals(order.getOrderState())) {
                        //主订单支付失败，子订单也支付失败
                        orderItem.setOrderState(OrderStateType.Failed);
                    }
                }
                if(isErp){
                    // 会员折扣后的尾程派送费,如果不存在会员信息,那么返回原始的尾程派送费
                    calculateSuccessFlow(tenantId, order, productSkuCode, orderItemPrice, orderItem, localeMessage, logisticsType, totalQuantity, finalDeliveryFeeFromErp, productSku, productSkuPrice, memberPrice);
                }else{
                    // 和尾程派送费相关的都设置为null
                    // 会员折扣后的尾程派送费,如果不存在会员信息,那么返回原始的尾程派送费
                    calculateFailedFlow(order, productSkuCode, orderItemPrice, orderItem, localeMessage, logisticsType, totalQuantity, productSkuPrice, memberPrice, finalDeliveryFeeFromErp);
                }
            }else{
                // 一口价不给仓库,但是仓库还是要塞默认值
                boolean aCase = false;
                ChannelTypeEnum channelType = order.getChannelType();
                list = getCarrier(logisticsCompanyName, channelType, aCase, list);
                memberSuccessFlow(tenantId, order, productSkuCode, orderItemPrice, orderItem, localeMessage, logisticsType, totalQuantity, finalDeliveryFeeFromErp, productSku, productSkuPrice, memberPrice);
                // 仓库可配送国家判断
                List<Warehouse> deliverableWarehouse = new ArrayList<>();
                for(String warehouseCode : stashList){
                    LambdaQueryWrapper<Warehouse> eq = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, productSku.getTenantId())
                                                                                          .eq(Warehouse::getWarehouseCode, warehouseCode)
                                                                                          .eq(Warehouse::getDelFlag, 0);
                    List<Warehouse> warehouses = TenantHelper.ignore(() -> iWarehouseService.list(eq));
                    if(CollUtil.isNotEmpty(warehouses)){
                        for (Warehouse warehouse : warehouses){
                            List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(warehouse.getWarehouseSystemCode()));
                            if(CollUtil.isNotEmpty(countryCodeList)){
                                if(countryCodeList.contains(countryCode)){
                                    deliverableWarehouse.add(warehouse);
                                }
                            }
                        }
                    }
                }
                // 判断可配送订单的仓库
                if(CollUtil.isEmpty(deliverableWarehouse)){
                    JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON();
                    order.setPayErrorMessage(errorMsg);
                    localeMessage.append(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                    return orderItemPrice;
                }
                // 仓库默认信息
//                String orgWarehouseCode = stashList.get(0);
//                LambdaQueryWrapper<Warehouse> eq = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, productSku.getTenantId())
//                                                                                      .eq(Warehouse::getWarehouseCode, orgWarehouseCode)
//                                                                                      .eq(Warehouse::getDelFlag, 0);
//                List<Warehouse> warehouses = TenantHelper.ignore(() -> iWarehouseService.list(eq));
//                try{
//                    if (CollUtil.isEmpty(warehouses)||warehouses.size()>1){
//                        throw new StockException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS.args(orgWarehouseCode));
//                    }
//                }
//                catch (StockException e){
//                    if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
//                        order.setExceptionCode(OrderExceptionEnum.stock_same_warehouse_code_exists.getValue());
//                        LocaleMessage localeMessage2 = e.getLocaleMessage();
//                        JSONObject errorMsg = localeMessage2.toJSON();
//                        order.setPayErrorMessage(errorMsg);
//                        order.setOrderState(OrderStateType.Failed);
//                        localeMessage.append(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS.args(orgWarehouseCode));
//                    }
//                }
//                Warehouse warehouse = warehouses.get(0);
//                paramDTO.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
//                if (CollUtil.isNotEmpty(list)){
//                    paramDTO.setLogisticsCarrierCode(list.get(0));
//                    paramDTO.setWarehouseCode(orgWarehouseCode);
//                }else {
//                    paramDTO.setLogisticsCarrierCode("Fedex");
//                    paramDTO.setLogisticsCode("FEDHD");
//                    paramDTO.setWarehouseCode(orgWarehouseCode);
//                }

                Warehouse warehouse = deliverableWarehouse.get(0);
                paramDTO.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                if (CollUtil.isNotEmpty(list)){
                    paramDTO.setLogisticsCarrierCode(list.get(0));
                    paramDTO.setWarehouseCode(warehouse.getWarehouseCode());
                }else {
                    paramDTO.setLogisticsCarrierCode("Fedex");
                    paramDTO.setLogisticsCode("FEDHD");
                    paramDTO.setWarehouseCode(warehouse.getWarehouseCode());
                }
            }
        }


        // erp 接口出现尾程异常  自提逻辑不需要加配送费逻辑

        return orderItemPrice;
    }

    /**
     * 功能描述：计算故障流
     *
     * @param order                   秩序
     * @param productSkuCode          产品sku代码
     * @param orderItemPrice          订购商品价格
     * @param orderItem               订购项目
     * @param localeMessage           区域设置消息
     * @param logisticsType           物流类型
     * @param totalQuantity           总数量
     * @param productSkuPrice         产品sku价格
     * @param memberPrice             会员价
     * @param finalDeliveryFeeFromErp erp最终交付费用
     * <AUTHOR>
     * @date 2024/10/08
     */
    private void calculateFailedFlow(Orders order, String productSkuCode, OrderItemPrice orderItemPrice, OrderItem orderItem,
                                     LocaleMessage localeMessage, LogisticsTypeEnum logisticsType, Integer totalQuantity,
                                     ProductSkuPrice productSkuPrice, RuleLevelProductPrice memberPrice,
                                     BigDecimal finalDeliveryFeeFromErp) {
        BigDecimal originalPickUpPrice = null;
        BigDecimal originalDropShippingPrice = null;
        originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();

        BigDecimal platformPickUpPrice = null;
        // 需要判断渠道
        if(OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource())){
            platformPickUpPrice = getPlatformPickUpPriceNotChannelType(orderItem, productSkuPrice);
        }else {
            platformPickUpPrice = getPlatformPickUpPrice(orderItem, productSkuPrice);
        }


        BigDecimal platformDropShippingPrice = null;
        // 非折扣单价+操作费+派送费

        platformDropShippingPrice = null;
        orderItemPrice.setOriginalDropShippingPrice(null);
        orderItemPrice.setPlatformDropShippingPrice(null);

        BigDecimal platformFinalDeliveryFee = null;
        // 不从产品价格获取,实时调用erp
//            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
        if (ObjectUtil.isNotEmpty(memberPrice)) {
            BigDecimal originalOperationFee1 = memberPrice.getOriginalOperationFee();
            BigDecimal originalUnitPrice1 = memberPrice.getOriginalUnitPrice();
            BigDecimal originalPickUpPrice1 = memberPrice.getOriginalPickUpPrice();

            BigDecimal platformUnitPrice = memberPrice.getPlatformUnitPrice();
            BigDecimal platformOperationFee = memberPrice.getPlatformOperationFee();

            if (ObjectUtil.isNotEmpty(originalOperationFee1) && ObjectUtil.isNotEmpty(originalUnitPrice1) ){
                if(ObjectUtil.isNotEmpty(finalDeliveryFeeFromErp)){
                    originalDropShippingPrice = originalOperationFee1.add(originalUnitPrice1).add(finalDeliveryFeeFromErp);
                }else{
                    originalDropShippingPrice = null;
                }

            }
            if (ObjectUtil.isNotEmpty(originalOperationFee1)&&ObjectUtil.isNotEmpty(originalPickUpPrice1)) {
                originalPickUpPrice = originalPickUpPrice1;
                orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
                orderItemPrice.setOriginalOperationFee(originalOperationFee1);
                orderItemPrice.setOriginalUnitPrice(originalUnitPrice1);
                orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
                orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
            }

            if (ObjectUtil.isNotEmpty(platformUnitPrice)&&ObjectUtil.isNotEmpty(platformOperationFee)) {
                platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
                orderItemPrice.setPlatformUnitPrice(originalUnitPrice1);
                if(ObjectUtil.isNotEmpty(finalDeliveryFeeFromErp)){
                    platformDropShippingPrice = platformUnitPrice.add(platformOperationFee).add(finalDeliveryFeeFromErp) ;
                }else{
                    platformDropShippingPrice = null;
                }

                orderItemPrice.setPlatformOperationFee(platformOperationFee);
                orderItemPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeFromErp);
                orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
            }

            if (ObjectUtil.isNotEmpty(memberPrice.getPlatformUnitPrice())) {
                orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
            }
        }

        // 平台自提价（平台+分销商，产品单价+操作费）

//            BigDecimal platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();


        BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        BigDecimal platformPayableUnitPrice = null;
        if(ObjectUtil.isNotEmpty(platformPickUpPrice)){
            platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        }

        BigDecimal originalPayableTotalAmount = null;
        // 原始实付总金额
        BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
        // 平台应付总金额
        BigDecimal platformPayableTotalAmount = null;
        // 平台应付总金额
        BigDecimal platformActualTotalAmount = null;
        // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            if(ObjectUtil.isNotEmpty(platformFinalDeliveryFee)){
                if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                    // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                    localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
                }
            }else {
                if(ObjectUtil.isNotEmpty(originalDropShippingPrice)){
                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
                    // 普通订单不需要支付订金，所以应付金额和实付金额是一样的

                    originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
                    originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
                    platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
                    platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
                }else {
                    originalPayableUnitPrice = null;
                    platformPayableUnitPrice = null;
                    originalActualTotalAmount = null;
                }

            }
        }else if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
            originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
        }

        // 供货商应得收入就是原始应付总金额 tag lty
        orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
        orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);

        orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);

        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
        orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        //平台实际支付单价（平台、分销商）
        orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        //平台实际支付总金额（平台、分销商）
        orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);

        orderItem.setPlatformRefundExecutableAmount(originalActualTotalAmount);

        orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);

    }

    /**
     * 功能描述：计算成功流
     *
     * @param tenantId                租户id
     * @param order                   秩序
     * @param productSkuCode          产品sku代码
     * @param orderItemPrice          订购商品价格
     * @param orderItem               订购项目
     * @param localeMessage           区域设置消息
     * @param logisticsType           物流类型
     * @param totalQuantity           总数量
     * @param finalDeliveryFeeFromErp erp最终交付费用
     * @param productSku              产品sku
     * @param productSkuPrice         产品sku价格
     * @param memberPrice             会员价
     * <AUTHOR>
     * @date 2024/10/08
     */
    private void calculateSuccessFlow(String tenantId, Orders order, String productSkuCode, OrderItemPrice orderItemPrice,
                                      OrderItem orderItem, LocaleMessage localeMessage, LogisticsTypeEnum logisticsType,
                                      Integer totalQuantity, BigDecimal finalDeliveryFeeFromErp, ProductSku productSku,
                                      ProductSkuPrice productSkuPrice, RuleLevelProductPrice memberPrice) {
        ProductPriceResponse resp = getProductPrice(productSku.getProductSkuCode(), finalDeliveryFeeFromErp, tenantId, order.getSiteId());
        finalDeliveryFeeFromErp = resp.getDeliveryFee();
        orderItemPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeFromErp);
        orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);

        BigDecimal originalPickUpPrice = null;
        BigDecimal originalDropShippingPrice = null;
        BigDecimal platformPickUpPrice = null;
        originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
//                platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
        // 会员价逻辑
        BigDecimal originalUnitPrice = productSkuPrice.getOriginalUnitPrice();
        BigDecimal originalOperationFee = productSkuPrice.getOriginalOperationFee();
        // 此处要加入会员逻辑
        originalDropShippingPrice = originalUnitPrice.add(originalOperationFee).add(finalDeliveryFeeFromErp);
        // todo 自提需要 优化
        if(OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource())){
            platformPickUpPrice = getPlatformPickUpPriceNotChannelType(orderItem, productSkuPrice);
        }else {
            platformPickUpPrice = getPlatformPickUpPrice(orderItem, productSkuPrice);
        }

        BigDecimal platformDropShippingPrice = null;
        // 非折扣单价+操作费+派送费
        platformDropShippingPrice = productSkuPrice.getPlatformPickUpPrice().add(finalDeliveryFeeFromErp);

        BigDecimal platformFinalDeliveryFee = finalDeliveryFeeFromErp;
        // 不从产品价格获取,实时调用erp
//            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
        if (ObjectUtil.isNotEmpty(memberPrice)) {
            BigDecimal originalOperationFee1 = memberPrice.getOriginalOperationFee();
            BigDecimal originalUnitPrice1 = memberPrice.getOriginalUnitPrice();
            BigDecimal originalPickUpPrice1 = memberPrice.getOriginalPickUpPrice();

            BigDecimal platformUnitPrice = memberPrice.getPlatformUnitPrice();
            BigDecimal platformOperationFee = memberPrice.getPlatformOperationFee();

            if (ObjectUtil.isNotEmpty(originalOperationFee1) && ObjectUtil.isNotEmpty(originalUnitPrice1) ){
                originalDropShippingPrice = originalOperationFee1.add(originalUnitPrice1).add(finalDeliveryFeeFromErp);
            }
            if (ObjectUtil.isNotEmpty(originalOperationFee1)&&ObjectUtil.isNotEmpty(originalPickUpPrice1)) {
                originalPickUpPrice = originalPickUpPrice1;
                orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
                orderItemPrice.setOriginalOperationFee(originalOperationFee1);
                orderItemPrice.setOriginalUnitPrice(originalUnitPrice1);
                orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
                orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
            }

            if (ObjectUtil.isNotEmpty(platformUnitPrice)&&ObjectUtil.isNotEmpty(platformOperationFee)) {
                platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
                orderItemPrice.setPlatformUnitPrice(originalUnitPrice1);
                platformDropShippingPrice = platformUnitPrice.add(platformOperationFee).add(finalDeliveryFeeFromErp) ;
                orderItemPrice.setPlatformOperationFee(platformOperationFee);

                orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
            }

            if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
                orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
            }
        }

        // 平台自提价（平台+分销商，产品单价+操作费）

//            BigDecimal platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();


        BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
        // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
            } else {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
            }
        }
//                if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
//                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
//                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
//                }
        // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
        // 原始应付总金额
        BigDecimal originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 原始实付总金额
        BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
        // 平台应付总金额
        BigDecimal platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
        // 平台应付总金额
        BigDecimal platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);

        // 供货商应得收入就是原始应付总金额 tag lty
        orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
        orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);
        orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
        orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);

        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
        orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        //平台实际支付单价（平台、分销商）
        orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        //平台实际支付总金额（平台、分销商）
        orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);
        orderItem.setPlatformRefundExecutableAmount(originalActualTotalAmount);

        orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
    }

//    /**
//     * 功能描述：折扣后获得原始最终运费v2
//     *
//     * @param sTenantId               s租户id
//     * @param dTenantId               d租户id
//     * @param finalDeliveryFeeFromErp erp最终交付费用
//     * @param isCalculate             正在计算
//     * @param productSkuId
//     * @return {@link BigDecimal }
//     * <AUTHOR>
//     * @date 2024/10/08
//     */
//    private BigDecimal getOriginalFinalDeliveryFeeAfterDiscountV2(String sTenantId, String dTenantId,
//                                                                  BigDecimal finalDeliveryFeeFromErp,
//                                                                  Boolean isCalculate,
//                                                                  Long productSkuId) {
//        Boolean isMember = iMemberRuleRelationService.isMember(sTenantId,dTenantId);
//        // 通过dictCode来
//        MemberDiscount calculateMember = iMemberDiscountV2Service.getCalculateMember(sTenantId, dTenantId);
//        boolean isCalculateMember = ObjectUtil.isNotEmpty(calculateMember);
//        BigDecimal discountFactor =null;
//        if(isCalculateMember){
//            Integer memberDiscount = calculateMember.getMemberDiscount();
//            discountFactor = BigDecimal.valueOf(memberDiscount).divide(new BigDecimal("100"),2, RoundingMode.HALF_UP);
//        }
////        finalDeliveryFeeFromErp.add(finalDeliveryFeeFromErp.multiply(discountFactor)).setScale(2, RoundingMode.HALF_UP);
////        sum = finalDeliveryFeeFromErp.multiply(BigDecimal.valueOf(1.13)).setScale(2, RoundingMode.HALF_UP);
//        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(sTenantId, dTenantId, productSkuId);
//        if(isCalculate){
//            if(isMember){
//                if(isCalculateMember){
//                    // 走会员自提+测算*1~1.13
//                    finalDeliveryFeeFromErp = finalDeliveryFeeFromErp.add(finalDeliveryFeeFromErp.multiply(discountFactor)).setScale(2, RoundingMode.HALF_UP);
//                }else {
//                    // 走会员自提+测算*1.13
//
//                }
//
//            }else {
//                // 走原价自提+测算*1.13
//            }
//        }else {
//            if(isMember){
//                // 走会员一口价
//
//            }else {
//                // 默认商品价格
//            }
//
//        }
//        return null;
//    }


    /**
     * 功能描述：会员价流程: 尾程折扣/一口价
     * 此方法受欧洲站点改造影响
     * @param tenantId                租户id
     * @param order                   秩序
     * @param productSkuCode          产品sku代码
     * @param orderItemPrice          订购商品价格
     * @param orderItem               订购项目
     * @param localeMessage           区域设置消息
     * @param logisticsType           物流类型
     * @param totalQuantity           总数量
     * @param finalDeliveryFeeFromErp erp最终交付费用
     * @param productSku              产品sku
     * @param productSkuPrice         产品sku价格
     * @param memberPrice             会员价
     * <AUTHOR>
     * @date 2024/09/30
     */
    private void memberSuccessFlow(String tenantId, Orders order, String productSkuCode, OrderItemPrice orderItemPrice,
                                      OrderItem orderItem, LocaleMessage localeMessage, LogisticsTypeEnum logisticsType,
                                      Integer totalQuantity, BigDecimal finalDeliveryFeeFromErp, ProductSku productSku,
                                      ProductSkuPrice productSkuPrice, RuleLevelProductPrice memberPrice) {
        // 判断是一口价还是会员价  会员模块对供应商的id,进行判断,如果是会员返回memberPrice,如果不是返回null
        ProductPriceResponse productPrice = getProductPrice(productSkuCode, null, tenantId, order.getSiteId());
        finalDeliveryFeeFromErp = productPrice.getDeliveryFee();
//            finalDeliveryFeeFromErp = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, finalDeliveryFeeFromErp);
        orderItemPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeFromErp);
        orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
        BigDecimal originalPickUpPrice = null;
        BigDecimal originalDropShippingPrice = null;
        BigDecimal platformPickUpPrice = null;
        originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
//                platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
        // 会员价逻辑
        BigDecimal originalUnitPrice = productSkuPrice.getOriginalUnitPrice();
        BigDecimal originalOperationFee = productSkuPrice.getOriginalOperationFee();
        // 此处要加入会员逻辑
        originalDropShippingPrice = originalUnitPrice.add(originalOperationFee).add(finalDeliveryFeeFromErp);
        // todo 自提需要 优化
        if(OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource())){
            platformPickUpPrice = getPlatformPickUpPriceNotChannelType(orderItem, productSkuPrice);
        }else {
            platformPickUpPrice = getPlatformPickUpPrice(orderItem, productSkuPrice);
        }

        BigDecimal platformDropShippingPrice = null;
        // 非折扣单价+操作费+派送费
        platformDropShippingPrice = productSkuPrice.getPlatformPickUpPrice().add(finalDeliveryFeeFromErp);

        BigDecimal platformFinalDeliveryFee = finalDeliveryFeeFromErp;
        // 不从产品价格获取,实时调用erp
//            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
        if (ObjectUtil.isNotEmpty(memberPrice)) {
            BigDecimal originalOperationFee1 = memberPrice.getOriginalOperationFee();
            BigDecimal originalUnitPrice1 = memberPrice.getOriginalUnitPrice();
            BigDecimal originalPickUpPrice1 = memberPrice.getOriginalPickUpPrice();

            BigDecimal platformUnitPrice = memberPrice.getPlatformUnitPrice();
            BigDecimal platformOperationFee = memberPrice.getPlatformOperationFee();

            if (ObjectUtil.isNotEmpty(originalOperationFee1) && ObjectUtil.isNotEmpty(originalUnitPrice1) ){
                originalDropShippingPrice = originalOperationFee1.add(originalUnitPrice1).add(finalDeliveryFeeFromErp);
            }
            if (ObjectUtil.isNotEmpty(originalOperationFee1)&&ObjectUtil.isNotEmpty(originalPickUpPrice1)) {
                originalPickUpPrice = originalPickUpPrice1;
                orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
                orderItemPrice.setOriginalOperationFee(originalOperationFee1);
                orderItemPrice.setOriginalUnitPrice(originalUnitPrice1);
                orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
                orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
            }

            if (ObjectUtil.isNotEmpty(platformUnitPrice)&&ObjectUtil.isNotEmpty(platformOperationFee)) {
                platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
                orderItemPrice.setPlatformUnitPrice(originalUnitPrice1);
                platformDropShippingPrice = platformUnitPrice.add(platformOperationFee).add(finalDeliveryFeeFromErp) ;
                orderItemPrice.setPlatformOperationFee(platformOperationFee);

                orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
            }

            if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
                orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
            }
        }

        // 平台自提价（平台+分销商，产品单价+操作费）

//            BigDecimal platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();


        BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
        // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
            } else {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
            }
        }
//                if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
//                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
//                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
//                }
        // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
        // 原始应付总金额
        BigDecimal originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 原始实付总金额
        BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
        // 平台应付总金额
        BigDecimal platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
        // 平台应付总金额
        BigDecimal platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);

        // 供货商应得收入就是原始应付总金额 tag lty
        orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
        orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);
        orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
        orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);

        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
        orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        //平台实际支付单价（平台、分销商）
        orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        //平台实际支付总金额（平台、分销商）
        orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);

        orderItem.setPlatformRefundExecutableAmount(originalActualTotalAmount);

        orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
    }


//    private OrderItemPrice getOrderItemPrice(OrderPriceCalculateDTO paramDTO, String tenantId, String zipCode,
//                                             List<String> stashList, Orders order, String logisticsCompanyName,
//                                             String productSkuCode, OrderItemPrice orderItemPrice, OrderItem orderItem,
//                                             LocaleMessage localeMessage, LogisticsTypeEnum logisticsType,
//                                             Integer totalQuantity) {
//        ProductSkuPrice productSkuPrice = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCode(productSkuCode));
//        ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getById(productSkuPrice.getProductSkuId()));
//
//        Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));
//        log.info("计算子订单价格 productSkuCode = {} productSkuPrice = {}", productSkuCode, JSONUtil.toJsonStr(productSkuPrice));
//        // 结算会以product_sku_price
//        orderItemPrice = getOrderItemPriceByChannel(productSkuPrice, orderItem, orderItemPrice);
//        // 会员入口 此处orderItem
//        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId());
//        // erp尾程派送费
//        BigDecimal finalDeliveryFeeFromErp = null;
//        Boolean isErp = true;
//        List<String> list = new ArrayList<>();
//        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(LoginHelper.getTenantId(), 1);
//        try {
//            if(CollUtil.isEmpty(stashList)){
//                // 仓库清单为空,则不走erp,测算异常 订单应为测算异常
//                if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
//                    localeMessage.append(ZSMallStatusCodeEnum.MEASUREMENT_ANOMALY.args(productSkuCode));
//                    order.setExceptionCode(OrderExceptionEnum.measurement_anomaly.getValue());
//                }
//                order.setPlatformTotalFinalDeliveryFee(null);
//                order.setOriginalTotalFinalDeliveryFee(null);
//                isErp = false;
//                paramDTO.setWarehouseSystemCode(null);
//            }else {
//                // todo theo 是否测算
//                ChannelTypeEnum channelType = order.getChannelType();
//                boolean aCase = false;
//                list = getCarrier(logisticsCompanyName, channelType, aCase, list);
//                DeliveryFeeByErpResponse deliveryFeeFromErp = getDeliveryFeeFromErp(stashList, zipCode, productSkuCode, list, tenantId, productSku.getTenantId());
//                // todo 未来更换成指定物流
//                finalDeliveryFeeFromErp = deliveryFeeFromErp.getShippingFee();
//                String orgWarehouseCode = deliveryFeeFromErp.getOrgWarehouseCode();
//                String carrierCode = deliveryFeeFromErp.getCarrierCode();
//                String logisticsCode = deliveryFeeFromErp.getLogisticCode();
//                if(ObjectUtil.isEmpty(finalDeliveryFeeFromErp)){
//                    throw new Exception("erp接口异常");
//                }
//                LambdaQueryWrapper<Warehouse> eq = new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, productSku.getTenantId())
//                                                                                      .eq(Warehouse::getWarehouseCode, orgWarehouseCode)
//                                                                                      .eq(Warehouse::getDelFlag, 0);
//                Warehouse warehouse = TenantHelper.ignore(()-> iWarehouseService.getOne(eq));
//                paramDTO.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
//                paramDTO.setLogisticsCarrierCode(carrierCode);
//                paramDTO.setLogisticsCode(logisticsCode);
//                paramDTO.setWarehouseCode(orgWarehouseCode);
//            }
//        }catch (Exception e){
//            // 出现异常将金额设置为null
//            isErp = false;
//            if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
//                order.setExceptionCode(OrderExceptionEnum.final_delivery_fee_exception.getValue());
//                try {
//                    throw new DeliveryFeeException(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION.args(productSkuCode));
//                } catch (DeliveryFeeException ex) {
//                    LocaleMessage localeMessage2 = ex.getLocaleMessage();
//                    JSONObject errorMsg = localeMessage2.toJSON();
//                    order.setPayErrorMessage(errorMsg);
//                    order.setOrderState(OrderStateType.Failed);
//                    localeMessage.append(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION.args(productSkuCode));
//                }
//
//            }
//        }
//
//        // erp 接口出现尾程异常  自提逻辑不需要加配送费逻辑
//        if(isErp){
//            // 会员折扣后的尾程派送费,如果不存在会员信息,那么返回原始的尾程派送费
//            finalDeliveryFeeFromErp = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, finalDeliveryFeeFromErp);
//            orderItemPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeFromErp);
//            orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
//            BigDecimal originalPickUpPrice = null;
//            BigDecimal originalDropShippingPrice = null;
//            BigDecimal platformPickUpPrice = null;
//            originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
////                platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
//            // 会员价逻辑
//            BigDecimal originalUnitPrice = productSkuPrice.getOriginalUnitPrice();
//            BigDecimal originalOperationFee = productSkuPrice.getOriginalOperationFee();
//            // 此处要加入会员逻辑
//            originalDropShippingPrice = originalUnitPrice.add(originalOperationFee).add(finalDeliveryFeeFromErp);
//            // todo 自提需要 优化
//            if(OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource())){
//                platformPickUpPrice = getPlatformPickUpPriceNotChannelType(orderItem, productSkuPrice);
//            }else {
//                platformPickUpPrice = getPlatformPickUpPrice(orderItem, productSkuPrice);
//            }
//
//            BigDecimal platformDropShippingPrice = null;
//            // 非折扣单价+操作费+派送费
//            platformDropShippingPrice = productSkuPrice.getPlatformPickUpPrice().add(finalDeliveryFeeFromErp);
//
//            BigDecimal platformFinalDeliveryFee = finalDeliveryFeeFromErp;
//            // 不从产品价格获取,实时调用erp
////            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
//            if (ObjectUtil.isNotEmpty(memberPrice)) {
//                BigDecimal originalOperationFee1 = memberPrice.getOriginalOperationFee();
//                BigDecimal originalUnitPrice1 = memberPrice.getOriginalUnitPrice();
//                BigDecimal originalPickUpPrice1 = memberPrice.getOriginalPickUpPrice();
//
//                BigDecimal platformUnitPrice = memberPrice.getPlatformUnitPrice();
//                BigDecimal platformOperationFee = memberPrice.getPlatformOperationFee();
//
//                if (ObjectUtil.isNotEmpty(originalOperationFee1) && ObjectUtil.isNotEmpty(originalUnitPrice1) ){
//                    originalDropShippingPrice = originalOperationFee1.add(originalUnitPrice1).add(finalDeliveryFeeFromErp);
//                }
//                if (ObjectUtil.isNotEmpty(originalOperationFee1)&&ObjectUtil.isNotEmpty(originalPickUpPrice1)) {
//                    originalPickUpPrice = originalPickUpPrice1;
//                    orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
//                    orderItemPrice.setOriginalOperationFee(originalOperationFee1);
//                    orderItemPrice.setOriginalUnitPrice(originalUnitPrice1);
//                    orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
//                    orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
//                }
//
//                if (ObjectUtil.isNotEmpty(platformUnitPrice)&&ObjectUtil.isNotEmpty(platformOperationFee)) {
//                    platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
//                    orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
//                    orderItemPrice.setPlatformUnitPrice(originalUnitPrice1);
//                    platformDropShippingPrice = platformUnitPrice.add(platformOperationFee).add(finalDeliveryFeeFromErp) ;
//                    orderItemPrice.setPlatformOperationFee(platformOperationFee);
//
//                    orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
//                }
//
//                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
//                    orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
//                }
//            }
//
//            // 平台自提价（平台+分销商，产品单价+操作费）
//
////            BigDecimal platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
//
//
//            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
//            BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
//            // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
//            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
//                    // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
//                    localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
//                } else {
//                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
//                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
//                }
//            }
////                if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
////                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
////                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
////                }
//            // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
//            // 原始应付总金额
//            BigDecimal originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
//            // 原始实付总金额
//            BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
//            // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
//            // 平台应付总金额
//            BigDecimal platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
//            // 平台应付总金额
//            BigDecimal platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
//
//            // 供货商应得收入就是原始应付总金额 tag lty
//            orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
//            orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
//            orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
//            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
//            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
//            orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
//            orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
//            orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);
//            orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
//            orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
//            orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);
//
//            orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
//            orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
//            //平台实际支付单价（平台、分销商）
//            orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);
//
//            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
//            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
//            //平台实际支付总金额（平台、分销商）
//            orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);
//
//            orderItem.setPlatformRefundExecutableAmount(platformActualTotalAmount);
//
//            orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
//            orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
//        }else{
//            // 和尾程派送费相关的都设置为null
//            // 会员折扣后的尾程派送费,如果不存在会员信息,那么返回原始的尾程派送费
////                finalDeliveryFeeFromErp = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, finalDeliveryFeeFromErp);
//            BigDecimal originalPickUpPrice = null;
//            BigDecimal originalDropShippingPrice = null;
//            originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
//
//            BigDecimal platformPickUpPrice = null;
//            // 需要判断渠道
//            if(OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource())){
//                platformPickUpPrice = getPlatformPickUpPriceNotChannelType(orderItem, productSkuPrice);
//            }else {
//                platformPickUpPrice = getPlatformPickUpPrice(orderItem, productSkuPrice);
//            }
//
//
//            BigDecimal platformDropShippingPrice = null;
//            // 非折扣单价+操作费+派送费
//
//            platformDropShippingPrice = null;
//            orderItemPrice.setOriginalDropShippingPrice(null);
//            orderItemPrice.setPlatformDropShippingPrice(null);
//
//            BigDecimal platformFinalDeliveryFee = null;
//            // 不从产品价格获取,实时调用erp
////            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
//            if (ObjectUtil.isNotEmpty(memberPrice)) {
//                BigDecimal originalOperationFee1 = memberPrice.getOriginalOperationFee();
//                BigDecimal originalUnitPrice1 = memberPrice.getOriginalUnitPrice();
//                BigDecimal originalPickUpPrice1 = memberPrice.getOriginalPickUpPrice();
//
//                BigDecimal platformUnitPrice = memberPrice.getPlatformUnitPrice();
//                BigDecimal platformOperationFee = memberPrice.getPlatformOperationFee();
//
//                if (ObjectUtil.isNotEmpty(originalOperationFee1) && ObjectUtil.isNotEmpty(originalUnitPrice1) ){
//                    if(ObjectUtil.isNotEmpty(finalDeliveryFeeFromErp)){
//                        originalDropShippingPrice = originalOperationFee1.add(originalUnitPrice1).add(finalDeliveryFeeFromErp);
//                    }else{
//                        originalDropShippingPrice = null;
//                    }
//
//                }
//                if (ObjectUtil.isNotEmpty(originalOperationFee1)&&ObjectUtil.isNotEmpty(originalPickUpPrice1)) {
//                    originalPickUpPrice = originalPickUpPrice1;
//                    orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
//                    orderItemPrice.setOriginalOperationFee(originalOperationFee1);
//                    orderItemPrice.setOriginalUnitPrice(originalUnitPrice1);
//                    orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
//                    orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
//                }
//
//                if (ObjectUtil.isNotEmpty(platformUnitPrice)&&ObjectUtil.isNotEmpty(platformOperationFee)) {
//                    platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
//                    orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
//                    orderItemPrice.setPlatformUnitPrice(originalUnitPrice1);
//                    if(ObjectUtil.isNotEmpty(finalDeliveryFeeFromErp)){
//                        platformDropShippingPrice = platformUnitPrice.add(platformOperationFee).add(finalDeliveryFeeFromErp) ;
//                    }else{
//                        platformDropShippingPrice = null;
//                    }
//
//                    orderItemPrice.setPlatformOperationFee(platformOperationFee);
//                    orderItemPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeFromErp);
//                    orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
//                }
//
//                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformUnitPrice())) {
//                    orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
//                }
//            }
//
//            // 平台自提价（平台+分销商，产品单价+操作费）
//
////            BigDecimal platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
//
//
//            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
//            BigDecimal platformPayableUnitPrice = null;
//            if(ObjectUtil.isNotEmpty(platformPickUpPrice)){
//                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
//                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
//            }
//
//            BigDecimal originalPayableTotalAmount = null;
//            // 原始实付总金额
//            BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
//            // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
//            // 平台应付总金额
//            BigDecimal platformPayableTotalAmount = null;
//            // 平台应付总金额
//            BigDecimal platformActualTotalAmount = null;
//            // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
//            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                if(ObjectUtil.isNotEmpty(platformFinalDeliveryFee)){
//                    if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
//                        // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
//                        localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
//                    }
//                }else {
//                    if(ObjectUtil.isNotEmpty(originalDropShippingPrice)){
//                        originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
//                        platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
//                        // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
//
//                        originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
//                        originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
//                        platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
//                        platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
//                    }else {
//                        originalPayableUnitPrice = null;
//                        platformPayableUnitPrice = null;
//                        originalActualTotalAmount = null;
//                    }
//
//
//                }
//            }else if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
//                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
//                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
//                platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
//            }
//
//            // 供货商应得收入就是原始应付总金额 tag lty
//            orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
//            orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
//            orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
//            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
//            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
//            orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
//            orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
//            orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);
//
//            orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
//            orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);
//
//            orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
//            orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
//            //平台实际支付单价（平台、分销商）
//            orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);
//
//            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
//            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
//            //平台实际支付总金额（平台、分销商）
//            orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);
//
//            orderItem.setPlatformRefundExecutableAmount(platformActualTotalAmount);
//
//            orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
//            orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
//        }
//        return orderItemPrice;
//    }

    @Nullable
    public static List<String> getCarrier(String logisticsCompanyName, ChannelTypeEnum channelType, boolean aCase,
                                           List<String> list) {
        if(ObjectUtil.isNotEmpty(channelType)&&ChannelTypeEnum.Walmart.equals(channelType)){
            if(ObjectUtil.isNotEmpty(logisticsCompanyName)){
                aCase = logisticsCompanyName.equalsIgnoreCase(CarrierTypeEnum.AMSP.getValue());
            }
            if(aCase ||ObjectUtil.isEmpty(logisticsCompanyName)){
                list.add(CarrierTypeEnum.FedEx.getValue());
                list.add(CarrierTypeEnum.UPS.getValue());
            }
        }
        if(ObjectUtil.isEmpty(channelType)&&ObjectUtil.isEmpty(logisticsCompanyName)){
            list.add(CarrierTypeEnum.FedEx.getValue());
            list.add(CarrierTypeEnum.UPS.getValue());
            list.add(CarrierTypeEnum.AMSP.getValue());
        }
        if(CollUtil.isEmpty(list)){
            if(StrUtil.isNotEmpty(logisticsCompanyName)){
                if (logisticsCompanyName.contains(",")){
                    list.addAll(StrUtil.splitTrim(logisticsCompanyName, ","));
                }else {
                    list = Collections.singletonList(logisticsCompanyName);
                }
            }
        }
        return list;
    }

    /**
     * 功能描述：按渠道获取订单商品价格
     *
     * @param productSkuPrice 产品SKU价格
     * @param orderItem       订单项
     * @param orderItemPrice
     * @return {@link OrderItemPrice }
     * <AUTHOR>
     * @date 2024/02/08
     */
    private OrderItemPrice getOrderItemPriceByChannel(ProductSkuPrice productSkuPrice, OrderItem orderItem,
                                                      OrderItemPrice orderItemPrice) {
        if (ChannelTypeEnum.Erp.equals(orderItem.getChannelType())) {
            BigDecimal upPrice = orderItemPrice.getPlatformPickUpPrice();
            BeanUtil.copyProperties(productSkuPrice, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime,platformPickUpPrice", "platformUnitPrice", "platformDropShippingPrice");
            orderItemPrice.setPlatformPickUpPrice(upPrice);
        } else {
            BeanUtil.copyProperties(productSkuPrice, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime","platformDropShippingPrice","originalFinalDeliveryFee",
                "platformFinalDeliveryFee",
                "originalDropShippingPrice"
            );
        }
        return orderItemPrice;
    }

    /**
     * 功能描述：获取平台取货价格 tiktok和erp取货价格为平台实际支付单价 其他渠道取货价格为产品sku价格,需要根据订单来源进行判断
     *
     * @param orderItem       订单项
     * @param productSkuPrice 产品SKU价格
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/02/07
     */
    private BigDecimal getPlatformPickUpPrice(OrderItem orderItem, ProductSkuPrice productSkuPrice) {

        BigDecimal platformPickUpPrice = BigDecimal.ZERO;
        platformPickUpPrice = orderItem.getPlatformActualUnitPrice();
        return platformPickUpPrice;
    }

    private BigDecimal getPlatformPickUpPriceNotChannelType(OrderItem orderItem, ProductSkuPrice productSkuPrice) {
        BigDecimal platformPickUpPrice = BigDecimal.ZERO;
        platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
        return platformPickUpPrice;
    }


    /**
     * 功能描述：会员后获得底价,如果没有会员价就返回原始价格
     *
     * @param stenantId    供应商
     * @param tenantId     分销商租户id
     * @param productSkuId 产品sku id
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/08/13
     */
    @Deprecated
    public BigDecimal getBasePriceAfterMember(String stenantId, String tenantId, Long productSkuId) {
//        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(stenantId, tenantId, productSkuId, );
//        if(ObjectUtil.isNotEmpty(memberPrice)){
//            return memberPrice.getPlatformPickUpPrice();
//        }
        // 暂时用不到先注释
//        ProductSkuPrice price = TenantHelper.ignore(()->iProductSkuPriceService.queryByProductSkuId(productSkuId));
//        if (ObjectUtil.isNotEmpty(price)){
//            return price.getPlatformPickUpPrice();
//        }
        return null;
    }

    /**
     * @description: 获取商品相关价格
     * @author: Len
     * @date: 2024/9/30 10:34
     * @param: productSkuCode 商品编码
     * @param: deliveryFee 外部运费
     * @param: tenantId  分销商租户ID
     * @return: com.zsmall.product.entity.domain.ProductPriceResponse
     **/
    public ProductPriceResponse getProductPrice(String productSkuCode, BigDecimal deliveryFee, String tenantId,
                                                Long siteId) {
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (ObjectUtil.isNull(productSku)){
            throw new RuntimeException(StrUtil.format("[获取商品价格]失败,商品信息不存在：{}", productSkuCode));
        }
        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,siteId);
        if (ObjectUtil.isNull(productSkuPrice)){
            throw new RuntimeException(StrUtil.format("[获取商品价格]失败,商品价格或商品站点价格信息不存在：{}", productSkuCode));
        }

        //供应商单价
        BigDecimal supplierProductPrice = productSkuPrice.getOriginalUnitPrice()==null?BigDecimal.ZERO:productSkuPrice.getOriginalUnitPrice();
        //分销商单价
        BigDecimal distributorProductPrice = productSkuPrice.getPlatformUnitPrice()==null?BigDecimal.ZERO:productSkuPrice.getPlatformUnitPrice();
        //分销商操作费
        BigDecimal supplierOperationFee = productSkuPrice.getPlatformOperationFee()==null?BigDecimal.ZERO:productSkuPrice.getPlatformOperationFee();
        //供应商操作费
        BigDecimal distributorOperationFee = productSkuPrice.getOriginalOperationFee()==null?BigDecimal.ZERO:productSkuPrice.getOriginalOperationFee();
        //尾程派送费
        BigDecimal finalDeliveryFee = BigDecimal.ZERO;
        //供应商会员单价
        BigDecimal supplierMemberProductPrice = BigDecimal.ZERO;
        //分销商会员单价
        BigDecimal distributorMemberProductPrice=BigDecimal.ZERO ;
        //分销商会员操作费
        BigDecimal supplierMemberOperationFee=BigDecimal.ZERO;
        //供应商会员操作费
        BigDecimal distributorMemberOperationFee=BigDecimal.ZERO ;
        boolean isMember=false;
        SysTenantVo tenantVo = sysTenantService.queryByTenantId(tenantId);
        if (ObjectUtil.isNotNull(deliveryFee)){
            finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, deliveryFee);
        }
        //获取会员价
        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(productSku.getTenantId(), tenantId, productSku.getId(), siteId);
        if (ObjectUtil.isNotNull(memberPrice)) {
            isMember=true;
            if (ObjectUtil.isNull(deliveryFee)){
                if (ObjectUtil.equal(tenantVo.getTenantType(), TenantType.Distributor.name())){
                    finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, memberPrice.getPlatformFinalDeliveryFee());
                }else {
                    finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, memberPrice.getOriginalFinalDeliveryFee());
                }
            }
            supplierMemberProductPrice=memberPrice.getOriginalUnitPrice()==null?BigDecimal.ZERO:memberPrice.getOriginalUnitPrice();
            distributorMemberProductPrice=memberPrice.getPlatformUnitPrice()==null?BigDecimal.ZERO:memberPrice.getPlatformUnitPrice();
            supplierMemberOperationFee=memberPrice.getOriginalOperationFee()==null?BigDecimal.ZERO:memberPrice.getOriginalOperationFee();
            distributorMemberOperationFee=memberPrice.getPlatformOperationFee()==null?BigDecimal.ZERO:memberPrice.getPlatformOperationFee();
        }else {
            if (ObjectUtil.isNull(deliveryFee)){
                if (ObjectUtil.equal(tenantVo.getTenantType(), TenantType.Distributor.name())){
                    finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, productSkuPrice.getPlatformFinalDeliveryFee());
                }else {
                    finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, productSkuPrice.getOriginalFinalDeliveryFee());
                }
            }

        }

        ProductPriceResponse deliveryFeeResponse = new ProductPriceResponse();
        deliveryFeeResponse.setDeliveryFee(finalDeliveryFee);
        deliveryFeeResponse.setProductSkuCode(productSku.getProductSkuCode());
        deliveryFeeResponse.setSku(productSku.getSku());
        deliveryFeeResponse.setIsMember(isMember);

        deliveryFeeResponse.setSupplierUnitPrice(supplierProductPrice);
        deliveryFeeResponse.setDistributorUnitPrice(distributorProductPrice);

        deliveryFeeResponse.setSupplierOperationFee(supplierOperationFee);
        deliveryFeeResponse.setDistributorOperationFee(distributorOperationFee);

        deliveryFeeResponse.setSupplierDropShippingPrice(supplierProductPrice.add(supplierOperationFee).add(finalDeliveryFee));
        deliveryFeeResponse.setDistributorDropShippingPrice(distributorProductPrice.add(distributorOperationFee).add(finalDeliveryFee));

        deliveryFeeResponse.setSupplierPickUpPrice(supplierProductPrice.add(supplierOperationFee));
        deliveryFeeResponse.setDistributorPickUpPrice(distributorProductPrice.add(distributorOperationFee));

        if (isMember){
            deliveryFeeResponse.setSupplierMemberUnitPrice(supplierMemberProductPrice);
            deliveryFeeResponse.setDistributorMemberUnitPrice(distributorMemberProductPrice);
            deliveryFeeResponse.setDistributorMemberOperationFee(distributorMemberOperationFee);
            deliveryFeeResponse.setSupplierMemberOperationFee(supplierMemberOperationFee);
            deliveryFeeResponse.setSupplierMemberDropShippingPrice(supplierMemberProductPrice.add(supplierMemberOperationFee).add(finalDeliveryFee));
            deliveryFeeResponse.setSupplierMemberPickUpPrice(supplierMemberProductPrice.add(supplierMemberOperationFee));
            deliveryFeeResponse.setDistributorMemberDropShippingPrice(distributorMemberProductPrice.add(distributorMemberOperationFee).add(finalDeliveryFee));
            deliveryFeeResponse.setDistributorMemberPickUpPrice(distributorMemberProductPrice.add(distributorMemberOperationFee));
        }
        return deliveryFeeResponse;
    }

    /**
     * @description: 获取商品站点相关价格
     * @author: Len
     * @date: 2024/9/30 10:34
     * @param: productSkuCode 商品编码
     * @param: deliveryFee 外部运费
     * @param: tenantId  分销商租户ID
     * @param: site 站点
     * @return: com.zsmall.product.entity.domain.ProductPriceResponse
     **/
    public ProductPriceResponse getProductPriceBySite(String productSkuCode,BigDecimal deliveryFee, String tenantId,String site) {
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (ObjectUtil.isNull(productSku)){
            throw new RuntimeException(StrUtil.format("[获取商品价格]失败,商品信息不存在：{}", productSkuCode));
        }
        Map<String, ProductSkuPrice> productSkuSitePriceMapByCode = iProductSkuPriceService.getProductSkuSitePriceMapByCode(Set.of(productSkuCode));
        ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(productSkuCode + "-" + site);
        if (ObjectUtil.isNull(productSkuPrice)){
            throw new RuntimeException(StrUtil.format("[获取商品价格]失败,商品价格信息不存在：{}", productSkuCode));
        }

        //供应商单价
        BigDecimal supplierProductPrice = productSkuPrice.getOriginalUnitPrice()==null?BigDecimal.ZERO:productSkuPrice.getOriginalUnitPrice();
        //分销商单价
        BigDecimal distributorProductPrice = productSkuPrice.getPlatformUnitPrice()==null?BigDecimal.ZERO:productSkuPrice.getPlatformUnitPrice();
        //分销商操作费
        BigDecimal supplierOperationFee = productSkuPrice.getPlatformOperationFee()==null?BigDecimal.ZERO:productSkuPrice.getPlatformOperationFee();
        //供应商操作费
        BigDecimal distributorOperationFee = productSkuPrice.getOriginalOperationFee()==null?BigDecimal.ZERO:productSkuPrice.getOriginalOperationFee();
        //尾程派送费
        BigDecimal finalDeliveryFee = BigDecimal.ZERO;
        //供应商会员单价
        BigDecimal supplierMemberProductPrice = BigDecimal.ZERO;
        //分销商会员单价
        BigDecimal distributorMemberProductPrice=BigDecimal.ZERO ;
        //分销商会员操作费
        BigDecimal supplierMemberOperationFee=BigDecimal.ZERO;
        //供应商会员操作费
        BigDecimal distributorMemberOperationFee=BigDecimal.ZERO ;
        boolean isMember=false;
        SysTenantVo tenantVo = sysTenantService.queryByTenantId(tenantId);
        if (ObjectUtil.isNotNull(deliveryFee)){
            finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, deliveryFee);
        }
        //获取会员价
        Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode = iProductSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(org.elasticsearch.core.Set.of(productSkuCode), tenantId);
        RuleLevelProductPrice memberPrice = ruleLevelProductPriceSitePriceMapByCode.get(productSkuCode + "-" + site);
        if (ObjectUtil.isNotNull(memberPrice)) {
            isMember=true;
            if (ObjectUtil.isNull(deliveryFee)){
                if (ObjectUtil.equal(tenantVo.getTenantType(), TenantType.Distributor.name())){
                    finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, memberPrice.getPlatformFinalDeliveryFee());
                }else {
                    finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, memberPrice.getOriginalFinalDeliveryFee());
                }
            }
            supplierMemberProductPrice=memberPrice.getOriginalUnitPrice()==null?BigDecimal.ZERO:memberPrice.getOriginalUnitPrice();
            distributorMemberProductPrice=memberPrice.getPlatformUnitPrice()==null?BigDecimal.ZERO:memberPrice.getPlatformUnitPrice();
            supplierMemberOperationFee=memberPrice.getOriginalOperationFee()==null?BigDecimal.ZERO:memberPrice.getOriginalOperationFee();
            distributorMemberOperationFee=memberPrice.getPlatformOperationFee()==null?BigDecimal.ZERO:memberPrice.getPlatformOperationFee();
        }else {
            if (ObjectUtil.isNull(deliveryFee)){
                if (ObjectUtil.equal(tenantVo.getTenantType(), TenantType.Distributor.name())){
                    finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, productSkuPrice.getPlatformFinalDeliveryFee());
                }else {
                    finalDeliveryFee = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, productSkuPrice.getOriginalFinalDeliveryFee());
                }
            }

        }

        ProductPriceResponse deliveryFeeResponse = new ProductPriceResponse();
        deliveryFeeResponse.setDeliveryFee(finalDeliveryFee);
        deliveryFeeResponse.setProductSkuCode(productSku.getProductSkuCode());
        deliveryFeeResponse.setSku(productSku.getSku());
        deliveryFeeResponse.setIsMember(isMember);

        deliveryFeeResponse.setSupplierUnitPrice(supplierProductPrice);
        deliveryFeeResponse.setDistributorUnitPrice(distributorProductPrice);

        deliveryFeeResponse.setSupplierOperationFee(supplierOperationFee);
        deliveryFeeResponse.setDistributorOperationFee(distributorOperationFee);

        deliveryFeeResponse.setSupplierDropShippingPrice(supplierProductPrice.add(supplierOperationFee).add(finalDeliveryFee));
        deliveryFeeResponse.setDistributorDropShippingPrice(distributorProductPrice.add(distributorOperationFee).add(finalDeliveryFee));

        deliveryFeeResponse.setSupplierPickUpPrice(supplierProductPrice.add(supplierOperationFee));
        deliveryFeeResponse.setDistributorPickUpPrice(distributorProductPrice.add(distributorOperationFee));

        if (isMember){
            deliveryFeeResponse.setSupplierMemberUnitPrice(supplierMemberProductPrice);
            deliveryFeeResponse.setDistributorMemberUnitPrice(distributorMemberProductPrice);
            deliveryFeeResponse.setDistributorMemberOperationFee(distributorMemberOperationFee);
            deliveryFeeResponse.setSupplierMemberOperationFee(supplierMemberOperationFee);
            deliveryFeeResponse.setSupplierMemberDropShippingPrice(supplierMemberProductPrice.add(supplierMemberOperationFee).add(finalDeliveryFee));
            deliveryFeeResponse.setSupplierMemberPickUpPrice(supplierMemberProductPrice.add(supplierMemberOperationFee));
            deliveryFeeResponse.setDistributorMemberDropShippingPrice(distributorMemberProductPrice.add(distributorMemberOperationFee).add(finalDeliveryFee));
            deliveryFeeResponse.setDistributorMemberPickUpPrice(distributorMemberProductPrice.add(distributorMemberOperationFee));
        }
        return deliveryFeeResponse;
    }
}

