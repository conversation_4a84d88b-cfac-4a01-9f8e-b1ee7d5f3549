package com.zsmall.marketplace.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.calculate.entity.support.DeliveryFeeSupport;
import com.zsmall.calculate.entity.util.DeliveryFeeV2Utils;
import com.zsmall.common.domain.DeliveryFeeByErpRequest;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.common.domain.delivery.DeliveryFeeStock;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderSourceEnum;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.common.enums.orderImportRecord.ImportTypeEnum;
import com.zsmall.common.enums.orderImportRecord.OrderTempState;
import com.zsmall.common.enums.product.ProductTypeEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.util.DataUtil;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.marketplace.service.MpUserShippingCartService;
import com.zsmall.order.entity.domain.OrderImportRecord;
import com.zsmall.order.entity.domain.OrderImportTemp;
import com.zsmall.order.entity.iservice.IOrderImportRecordService;
import com.zsmall.order.entity.iservice.IOrderImportTempService;
import com.zsmall.order.entity.iservice.OrderCodeGenerator;
import com.zsmall.order.entity.iservice.OrderCodeGeneratorV2;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.biz.support.MemberSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.UserShippingCartBo;
import com.zsmall.product.entity.domain.bo.userShippingCart.ShippingCartOrderBo;
import com.zsmall.product.entity.domain.bo.userShippingCart.ShippingCartUpdateBo;
import com.zsmall.product.entity.domain.bo.userShippingCart.UserShippingCartAddBo;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.domain.vo.userShippingCart.ShippingCartInfoVo;
import com.zsmall.product.entity.domain.vo.userShippingCart.ShippingCartOrderReadyVo;
import com.zsmall.product.entity.domain.vo.userShippingCart.ShippingCartOrderVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.TenantWallet;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.domain.vo.TenantShippingAddressVo;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantShippingAddressService;
import com.zsmall.system.entity.iservice.ITenantWalletService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户购物车Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MpUserShippingCartServiceImpl implements MpUserShippingCartService {
    private final OrderCodeGeneratorV2 orderCodeGeneratorV2;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final PriceSupportV2 priceSupportV2;
    private final ISysConfigService sysConfigService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final ProductSkuStockService productSkuStockService;
    private final IUserShippingCartService iUserShippingCartService;
    private final ITenantWalletService iTenantWalletService;
    private final ITenantShippingAddressService iTenantShippingAddressService;
    private final IOrderImportRecordService iOrderImportRecordService;
    private final IOrderImportTempService iOrderImportTempService;
    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final IMemberLevelV2ServiceImpl iMemberLevelService;
    private final OrderCodeGenerator orderCodeGenerator;
    private final DeliveryFeeV2Utils deliveryFeeUtils;
    private final ProductSkuStockMapper productSkuStockMapper;
    private final MemberSupport memberSupport;
    private final IWarehouseService iWarehouseService;
    private final DeliveryFeeSupport deliveryFeeSupport;
    private final ISysTenantService sysTenantService;
    private final IProductSkuStockService iProductSkuStockService;
    /**
     * 查询用户购物车列表
     */
    @Override
    public R<List<ShippingCartInfoVo>> queryList(String site) {
        if (StrUtil.isEmpty(site)) {
            return R.fail("site不能为空");
        }
        List<UserShippingCart> list = iUserShippingCartService.list();
        Set<String> productSkuCodes = list.stream()
                                                       .map(UserShippingCart::getProductSkuCode) // 假设这是获取productSkuCode的方法
                                                       .collect(Collectors.toSet());
        Map<String, ProductSkuPrice> productSkuSitePriceMapByCode = iProductSkuPriceService.getProductSkuSitePriceMapByCode(productSkuCodes);
        Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode = iProductSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(productSkuCodes, LoginHelper.getTenantId());
        List<ShippingCartInfoVo> voList = new ArrayList<>();

        List<Long> productSkuIdList = new ArrayList<>();
        for (UserShippingCart userShippingCart : list) {
            ShippingCartInfoVo vo = new ShippingCartInfoVo();
            String productSkuCode = userShippingCart.getProductSkuCode();
            Integer quantity = userShippingCart.getQuantity();

            ProductSku productSku = iProductSkuService.queryByProductSkuCodeIncludeDel(productSkuCode);
            if (productSku == null) {
                continue;
            }
            vo.setProductSkuId(productSku.getId());
            productSkuIdList.add(productSku.getId());
//            if(StringUtils.isEmpty(ruleCustomizerTenantId)){
//                ruleCustomizerTenantId = productSku.getTenantId();
//            }

            Product product = iProductService.queryByIdIncludeDelete(productSku.getProductId());
            if (productSku == null) {
                continue;
            }

            Boolean available = false;
            if (ObjUtil.equals(productSku.getShelfState(), ShelfStateEnum.OnShelf) && StrUtil.equals(productSku.getDelFlag(), "0")
                && ObjUtil.equals(product.getShelfState(), ShelfStateEnum.OnShelf) && StrUtil.equals(product.getDelFlag(), "0")) {
                available = true;
            }

            //获取库存
            SkuStock skuStock =TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper()
                                                                              .getSkuStock(userShippingCart.getProductSkuCode()));
            if (ObjectUtil.isNull(skuStock)){
                vo.setPickUpStockTotal(0);
                vo.setDropShippingStockTotal(0);
            }else {
                vo.setPickUpStockTotal(skuStock.getPickUpStockTotal());
                vo.setDropShippingStockTotal(skuStock.getDropShippingStockTotal());
            }
            vo.setSupportedLogistics(userShippingCart.getSupportedLogistics());
            vo.setId(userShippingCart.getId());
            vo.setProductName(product.getName());
            vo.setProductCode(product.getProductCode());
            vo.setProductSkuCode(productSkuCode);
            vo.setQuantity(quantity);
            vo.setImageShowUrl(userShippingCart.getBeforeImageShowUrl());
            vo.setSpecValName(productSku.getSpecValName());
            vo.setAvailable(available);
            vo.setSku(productSku.getSku());
            BigDecimal productPrice=null;
            //处理会员价格
            ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(productSkuCode + "-" + site);
            if (ObjectUtil.isNotNull(productSkuPrice)){
                if (ObjUtil.equals(userShippingCart.getSupportedLogistics(), SupportedLogisticsEnum.DropShippingOnly.name())) {
                    vo.setUnitPrice(productSkuPrice.getPlatformDropShippingPrice());
                } else {
                    vo.setUnitPrice(productSkuPrice.getPlatformPickUpPrice());
                }
                productPrice=vo.getUnitPrice();
            }else {
                vo.setUnitPrice(null);
            }

            if (ObjectUtil.equal(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)){
                RuleLevelProductPrice ruleLevelProductPrice = ruleLevelProductPriceSitePriceMapByCode.get(productSkuCode + "-" + site);
                if (ObjectUtil.isNotNull(ruleLevelProductPrice)){
                    if (ObjUtil.equals(userShippingCart.getSupportedLogistics(), SupportedLogisticsEnum.DropShippingOnly.name())) {
                        vo.setMemberPrice(ruleLevelProductPrice.getPlatformDropShippingPrice());
                    }else {
                        vo.setMemberPrice(ruleLevelProductPrice.getPlatformPickUpPrice());
                    }
                    productPrice=vo.getMemberPrice();
                } else {
                    vo.setMemberPrice(null);
                }
            }

            if (ObjectUtil.isNotNull(productPrice)){
                vo.setTotalPrice(NumberUtil.mul(productPrice, quantity));
            }else {
                vo.setTotalPrice(null);
            }

            voList.add(vo);
        }
//        for(ShippingCartInfoVo shippingCartInfoVo : voList){
//            if(null != shippingCartInfoVo.getMemberPrice()){
//                shippingCartInfoVo.setTotalPrice(NumberUtil.mul(shippingCartInfoVo.getMemberPrice(), shippingCartInfoVo.getQuantity()));
//            }
//        }
        return R.ok(voList);
    }

    /**
     * 添加购物车
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> addToCart(UserShippingCartAddBo bo) {
        LoginHelper.getLoginUser(TenantType.Distributor);

        String productSkuCode = bo.getProductSkuCode();
        // Integer quantity = bo.getQuantity();
        if (StrUtil.isBlank(productSkuCode)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        if (StrUtil.isEmpty(bo.getSupportedLogistics())){
            return R.fail(ZSMallStatusCodeEnum.SUPPORTED_LOGISTICS_IS_NOT_NUll);
        }
        String[] supportedLogistics = new String[]{"PickUpOnly","DropShippingOnly"};
        if (!Arrays.asList(supportedLogistics).contains(bo.getSupportedLogistics())) {
           return R.fail(ZSMallStatusCodeEnum.ERROR_SUPPORTED_LOGISTICS);
        }
        // if (quantity <= 0) {
        //     return R.fail(ZSMallStatusCodeEnum.SHOPPING_CART_PRODUCT_NUM_AT_LEAST_ONE);
        // }

        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (productSku == null) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
        }
      //  ProductSkuPriceVo priceVo = iProductSkuPriceService.getBySkuId(productSku.getId());
        String imageUrl = iProductSkuAttachmentService.getPrimaryImgBySkuId(productSku.getId());

        Product product = iProductService.queryByIdNotTenant(productSku.getProductId());
        ProductTypeEnum productType = product.getProductType();
        if (ProductTypeEnum.WholesaleProduct.equals(productType)) {
            return R.fail(ZSMallStatusCodeEnum.WHOLESALE_CANNOT_ADD_TO_CART);
        }
        LambdaQueryWrapper<UserShippingCart> q = new LambdaQueryWrapper<>();
        q.eq(UserShippingCart::getProductSkuCode,bo.getProductSkuCode());
        q.eq(UserShippingCart::getSupportedLogistics,bo.getSupportedLogistics());
        UserShippingCart shippingCartVo = iUserShippingCartService.getOne(q);
        UserShippingCartBo userShippingCartBo = BeanUtil.copyProperties(shippingCartVo, UserShippingCartBo.class);

        Integer quantity;
        if (shippingCartVo == null) {
            userShippingCartBo = new UserShippingCartBo();
            userShippingCartBo.setUserId(LoginHelper.getUserId());
            userShippingCartBo.setProductSkuCode(productSku.getProductSkuCode());
            userShippingCartBo.setSupportedLogistics(bo.getSupportedLogistics());
            quantity = 0;
        } else {
            quantity = userShippingCartBo.getQuantity();
        }
        userShippingCartBo.setQuantity(quantity + 1);
        //判断数量
        SkuStock skuStock =TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper().getSkuStock(productSkuCode));
        int num = 0;
        if ("PickUpOnly".equals(bo.getSupportedLogistics())) {
            // 自提
            num = skuStock.getPickUpStockTotal();
        } else if ("DropShippingOnly".equals(bo.getSupportedLogistics())) {
            // 代发
            num = skuStock.getDropShippingStockTotal();
        }
        if (num < userShippingCartBo.getQuantity()) {
            return R.fail("当前购物车加购数量不能超过" + num);
        }
        userShippingCartBo.setBeforeImageShowUrl(imageUrl);
//        userShippingCartBo.setBeforePickUpPrice(priceVo.getPlatformPickUpPrice());
//        userShippingCartBo.setBeforeDropShippingPrice(priceVo.getPlatformDropShippingPrice());
//        userShippingCartBo.setBeforeMsrp(priceVo.getMsrp());
        userShippingCartBo.setBeforeStockQuantity(productSku.getStockTotal());

        if (ObjUtil.isNotNull(userShippingCartBo.getId())) {
            iUserShippingCartService.updateByBo(userShippingCartBo);
        } else {
            if (!iUserShippingCartService.insertByBo(userShippingCartBo)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.ADD_TO_SHOPPING_CART_ERROR);
            }
        }
        return R.ok();
    }

    /**
     * 查询购物车中商品种类
     */
    @Override
    public R<Long> queryCartQuantity() {
        LoginHelper.getLoginUser(TenantType.Distributor);
        return R.ok(iUserShippingCartService.queryAllValid());
    }

    /**
     * 修改用户购物车
     */
    @Override
    public Boolean updateByBo(ShippingCartUpdateBo bo) {
        if (ObjectUtil.isNull(bo.getId())){
            throw new RuntimeException("id不存在");
        }
        UserShippingCart byId = iUserShippingCartService.getById(bo.getId());
        if (byId == null) {
           throw  new RuntimeException("收藏夹商品数据不存在");
        }
        //校验数量
        SkuStock skuStock =TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper().getSkuStock(byId.getProductSkuCode()));
        int num=0;
        if (ObjectUtil.equal(byId.getSupportedLogistics(),"PickUpOnly")){
            num=skuStock.getPickUpStockTotal();
        }else if (ObjectUtil.equal(byId.getSupportedLogistics(),"DropShippingOnly")){
            num=skuStock.getDropShippingStockTotal();
        }
        if (num<bo.getQuantity()){
            throw new RuntimeException("当前商品最大加购数量"+num);
        }
        UserShippingCartBo bean = BeanUtil.toBean(bo, UserShippingCartBo.class);
        return iUserShippingCartService.updateByBo(bean);
    }

    /**
     * 校验并批量删除购物车信息
     *
     * @param ids
     * @param isValid
     * @return
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iUserShippingCartService.deleteWithValidByIds(ids, isValid);
    }

    /**
     * 购物车下单准备
     */
    @Override
    public R<ShippingCartOrderReadyVo> shippingCartOrderReady(String site) {
        LoginHelper.getLoginUser(TenantType.Distributor);

        SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(site);
        if (ObjectUtil.isNull(siteCountryCurrencyVo)){
            return R.fail("站点不存在");
        }
        if (StringUtils.isEmpty(siteCountryCurrencyVo.getCurrencyCode())){
            return R.fail("站点币种不存在");
        }
        List<TenantShippingAddressVo> tenantShippingAddressVos = iTenantShippingAddressService.queryList();

        TenantWallet tenantWallet = iTenantWalletService.queryByTenantId(LoginHelper.getTenantId(),siteCountryCurrencyVo.getCurrencyCode());
        if (ObjectUtil.isNull(tenantWallet)){
            return R.fail(siteCountryCurrencyVo.getCurrencyCode()+" 当前币种钱包不存在");
        }
        ShippingCartOrderReadyVo vo = new ShippingCartOrderReadyVo();
        vo.setAddressList(tenantShippingAddressVos);
        vo.setWalletBalance(tenantWallet.getWalletBalance());
        return R.ok(vo);
    }

    /**
     * 购物车下单
     */
    @Override
    public R<ShippingCartOrderVo> shippingCartOrder(ShippingCartOrderBo bo) throws Exception {
//        LoginHelper.getLoginUser(TenantType.Distributor);
        String dtenantId = LoginHelper.getTenantId();
//        String dtenantId = "DLTA1X9";
        String channelFlag = TenantHelper.ignore(() -> productSkuStockMapper.getChannelFlag(dtenantId));
        if(null == bo.getShippingAddressId()){
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        Long shippingAddressId = bo.getShippingAddressId();
        List<Long> shippingCartIds = bo.getShippingCartIds();

        TenantShippingAddressVo addressVo = iTenantShippingAddressService.queryById(shippingAddressId);
        if (addressVo == null) {
            return R.fail(ZSMallStatusCodeEnum.THE_RELEVANT_DATA_HAS_CHANGED);
        }
        String countryCode = addressVo.getCountryCode();
        Long siteId = null;
        SiteCountryCurrency siteByCountryCode = new SiteCountryCurrency();
        try {
            siteByCountryCode = iSiteCountryCurrencyService.getSiteByCountryCode(countryCode);
            siteId = siteByCountryCode.getId();
        }catch (Exception e) {
            log.error("获取站点信息失败,站点信息异常:{},异常信息-",countryCode,e);
            throw new RuntimeException("获取站点信息失败,站点信息异常:"+countryCode);
        }
        List<UserShippingCart> shippingCarts = iUserShippingCartService.getListByIds(shippingCartIds);
        List<String> productSkuCodes = shippingCarts.stream()
                                                 .map(UserShippingCart::getProductSkuCode)
                                                 .distinct()
                                                 .collect(Collectors.toList());
        //转map，key是productSkuCode,value是UserShippingCart
        Map<String, UserShippingCart> shippingCartMap = shippingCarts.stream()
                                                         .collect(Collectors.toMap(UserShippingCart::getProductSkuCode, Function.identity()));
        //shippingCarts 转成map,key是id,value是productSkuCode
//        Map<Long, String> shippingCartMap = shippingCarts.stream()
//                                                         .collect(Collectors.toMap(UserShippingCart::getId, UserShippingCart::getProductSkuCode));
        List<ProductSku> productSkus = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(productSkuCodes)){
            LambdaQueryWrapper<ProductSku> in = new LambdaQueryWrapper<ProductSku>().in(ProductSku::getProductSkuCode, productSkuCodes);
            productSkus = TenantHelper.ignore(()->iProductSkuService.list(in));
        }

        // 转换成map,key是productSkuCode value是 sku
//        Map<String, String> productSkuCodeMap = productSkus.stream()
//                                                       .collect(Collectors.toMap( ProductSku::getSku,ProductSku::getProductSkuCode, (oldValue, newValue) -> oldValue));
        // productSkus 转换成map key是productSkuCode value是productSku
        Map<String, ProductSku> productSkuMap = productSkus.stream()
                                                           .collect(Collectors.toMap(ProductSku::getProductSkuCode, Function.identity()));

        // 不一定只有一个 多个供应商就会有多个flag


        List<DeliveryFeeStock>deliveryFeeStocks =new ArrayList<>();
        for (UserShippingCart shippingCart : shippingCarts) {
            DeliveryFeeStock deliveryFeeStock = new DeliveryFeeStock();
            deliveryFeeStock.setProductSkuCode(shippingCart.getProductSkuCode());
            deliveryFeeStock.setTotalQuantity(shippingCart.getQuantity());
            deliveryFeeStock.setFlag(shippingCart.getProductSkuCode());
            deliveryFeeStocks.add(deliveryFeeStock);
        }

        // 拿到所有的flag对应productSkuCode todo 需要改造根据供应商维度分批次调用,sku维度也需要分批次调用
        HashMap<String, List<String>> stashMap = productSkuStockService.getStashList(deliveryFeeStocks);
        // 这里需要加入租户维度
        ConcurrentHashMap<String,ConcurrentHashMap<String,ProductSku>> productSkuPriceReqHashMap = new ConcurrentHashMap<>();
        ConcurrentHashMap<String,ConcurrentHashMap<String,DeliveryFeeByErpResponse>> productSkuPriceResultHashMap = new ConcurrentHashMap<>();
        // 不同供应商
        for (String productSkuCode : productSkuCodes) {

            ProductSku sku = productSkuMap.get(productSkuCode);
            String tenantId = sku.getTenantId();
            ConcurrentHashMap<String, ProductSku> skuMap = productSkuPriceReqHashMap.computeIfAbsent(tenantId, k -> new ConcurrentHashMap<>());
            skuMap.putIfAbsent(sku.getProductSkuCode(), sku);

        }
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(LoginHelper.getTenantId(), 1);
        // productSkuPriceHashMap遍历
        for (Map.Entry<String, ConcurrentHashMap<String, ProductSku>> entry : productSkuPriceReqHashMap.entrySet()) {
            String tenantId = entry.getKey();
            ConcurrentHashMap<String, ProductSku> skuMap = entry.getValue();
            // 在这里处理每个category和对应的skuMap , productSkuCode是不会重复的
            for (Map.Entry<String, ProductSku> skuEntry : skuMap.entrySet()) {
                List<DeliveryFeeByErpRequest> deliveryFeeByErpRequest = new ArrayList<>();
                List<DeliveryFeeByErpResponse> deliveryFeeByErp = new ArrayList<>();
                String productSkuCode = skuEntry.getKey();
                ProductSku sku = skuEntry.getValue();
                UserShippingCart shippingCart = shippingCartMap.get(productSkuCode);
                if(ObjectUtil.isNull(shippingCart)){
                   throw new RuntimeException("购物车商品不存在");
                }
                DeliveryFeeByErpRequest deliveryFeeReq = new DeliveryFeeByErpRequest();
                deliveryFeeReq.setChannelFlag(channelFlag);
                List<String> stashList = new ArrayList<>();
                if(CollUtil.isNotEmpty(stashMap)){
                    stashList = stashMap.get(productSkuCode);
                }
                if(CollUtil.isEmpty(stashList)){
                    continue;
                }
                deliveryFeeReq.setOrgWarehouseCodeList(stashList);
                deliveryFeeReq.setPostcode(addressVo.getZipCode());
                deliveryFeeReq.setSupplierTenantId(sku.getTenantId());
                deliveryFeeReq.setDistributorTenantId(dtenantId);
                deliveryFeeReq.setCountryCode(countryCode);
                DeliveryFeeByErpRequest.ProductItem productItem = new DeliveryFeeByErpRequest.ProductItem();
                productItem.setErpSku(sku.getSku());
                productItem.setQuantity(1);

                deliveryFeeReq.setSkuList(Collections.singletonList(productItem));
//                deliveryFeeReq.setLogisticCode(null);
                deliveryFeeByErpRequest.add(deliveryFeeReq);
                ConcurrentHashMap<String, DeliveryFeeByErpResponse> resultMap = new ConcurrentHashMap<>();
                DeliveryFeeByErpResponse deliveryFeeByErpResponse =new DeliveryFeeByErpResponse();
                if(approvedTenant && ObjectUtil.notEqual("PickUpOnly",shippingCart.getSupportedLogistics())){
                    deliveryFeeByErp = deliveryFeeUtils.getDeliveryFeeByErp(deliveryFeeByErpRequest, sku.getTenantId());
                    deliveryFeeByErpResponse = deliveryFeeByErp.get(0);
                }

                if (CollUtil.isNotEmpty(productSkuPriceResultHashMap) && productSkuPriceResultHashMap.containsKey(tenantId)) {
                    resultMap = productSkuPriceResultHashMap.get(tenantId);
                }
                resultMap.put(productSkuCode, deliveryFeeByErpResponse);
                if(ObjectUtil.isNotEmpty(resultMap)){
                    productSkuPriceResultHashMap.put(tenantId,resultMap);
                }
            }
        }
        // tenantId productSkuCode DeliveryFeeByErpResponse   tenantId productSkuCode  fee wareHouseCode obj
        ConcurrentHashMap<String,ConcurrentHashMap<String,DeliveryFeeStock> > deliveryFeeByErpMap = new ConcurrentHashMap<>();
        // 会员计价逻辑包含在内
        if(CollUtil.isNotEmpty(productSkuPriceResultHashMap)){
            for (Map.Entry<String, ConcurrentHashMap<String, DeliveryFeeByErpResponse>> resultHashMap : productSkuPriceResultHashMap.entrySet()) {
                ConcurrentHashMap<String, DeliveryFeeByErpResponse> value = resultHashMap.getValue();
                if (CollUtil.isNotEmpty(value)) {
                    for (Map.Entry<String, DeliveryFeeByErpResponse> erpResponseEntry : value.entrySet()) {
                        String key = erpResponseEntry.getKey();
                        DeliveryFeeByErpResponse response = erpResponseEntry.getValue();
                        if (ObjectUtil.isNotEmpty(response)) {
                            BigDecimal shippingFee = response.getShippingFee();
                            ProductSku productSku = productSkuMap.get(key);
                            if (ObjectUtil.isNotEmpty(productSku)) {
                                ProductPriceResponse productPrice = priceSupportV2.getProductPrice(productSku.getProductSkuCode(), shippingFee, dtenantId,siteId );
                                BigDecimal originalFinalDeliveryFeeAfterDiscount = productPrice.getDeliveryFee();
                                DeliveryFeeStock deliveryFeeStock = new DeliveryFeeStock();
                                if (originalFinalDeliveryFeeAfterDiscount != null) {
                                    deliveryFeeStock.setFee(originalFinalDeliveryFeeAfterDiscount);
                                }
                                String orgWarehouseCode = response.getOrgWarehouseCode();
                                if (orgWarehouseCode != null) {
                                    LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<Warehouse>()
                                        .eq(Warehouse::getWarehouseCode, orgWarehouseCode)
                                        .eq(Warehouse::getTenantId, productSku.getTenantId());
                                    Warehouse warehouse = TenantHelper.ignore(()->iWarehouseService.getOne(wrapper));
                                    if (ObjectUtil.isNotEmpty(warehouse)) {
                                        String warehouseSystemCode = warehouse.getWarehouseSystemCode();
                                        deliveryFeeStock.setWarehouseSysCode(warehouseSystemCode);
                                    }
                                }

                                // 注意：这里每次循环都创建新的ConcurrentHashMap并放入deliveryFeeByErpMap可能不是最佳实践
                                // 更好的做法可能是先检查deliveryFeeByErpMap中是否已有对应tenantId的map，如果没有则创建
                                ConcurrentHashMap<String, DeliveryFeeStock> respMap = deliveryFeeByErpMap.computeIfAbsent(productSku.getTenantId(), k -> new ConcurrentHashMap<>());
                                respMap.put(key, deliveryFeeStock);
                                deliveryFeeByErpMap.put(productSku.getTenantId(), respMap);
                            }
                        }else {
                            ProductSku productSku = productSkuMap.get(key);
                            if (ObjectUtil.isNotEmpty(productSku)) {
                                ProductPriceResponse productPrice = priceSupportV2.getProductPrice(productSku.getProductSkuCode(), null, dtenantId, siteId);
                                BigDecimal originalFinalDeliveryFeeAfterDiscount = productPrice.getDeliveryFee();
                                DeliveryFeeStock deliveryFeeStock = new DeliveryFeeStock();
                                if (originalFinalDeliveryFeeAfterDiscount != null) {
                                    deliveryFeeStock.setFee(originalFinalDeliveryFeeAfterDiscount);
                                }
                                ConcurrentHashMap<String, DeliveryFeeStock> respMap = deliveryFeeByErpMap.computeIfAbsent(productSku.getTenantId(), k -> new ConcurrentHashMap<>());
                                respMap.put(key, deliveryFeeStock);
                                deliveryFeeByErpMap.put(productSku.getTenantId(), respMap);
                            }
                        }
                    }
                }
            }

        }else {
            deliveryFeeByErpMap = null;
        }

        // deliveryFeeByErp 转换为map key是skuList内第一个元素的erpSku value是DeliveryFeeByErpResponse
        // deliveryFeeByErp 转换为key为productCode value 为 DeliveryFeeByErpResponse 的map
        List<OrderImportTemp> tempList = new ArrayList<>();
        for (Long shippingCartId : shippingCartIds) {
            UserShippingCart shippingCart = iUserShippingCartService.getById(shippingCartId);
            if (shippingCart == null) {
                return R.fail(ZSMallStatusCodeEnum.THE_RELEVANT_DATA_HAS_CHANGED);
            }

            String productSkuCode = shippingCart.getProductSkuCode();
            Integer quantity = shippingCart.getQuantity();

            ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
            if (productSku == null) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_OFF_SHELF.args(productSkuCode));
            }
            Product product = iProductService.queryById(productSku.getProductId());
            if (product == null) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_OFF_SHELF.args(productSkuCode));
            }

            if (ObjUtil.equals(product.getShelfState(), ShelfStateEnum.OnShelf) && ObjUtil.equals(productSku.getShelfState(), ShelfStateEnum.OnShelf)) {
                LogisticsTypeEnum logisticsType;
                if (!ObjectUtil.equals(shippingCart.getSupportedLogistics(), SupportedLogisticsEnum.PickUpOnly.name())) {
                    logisticsType = LogisticsTypeEnum.DropShipping;
                } else {
                    logisticsType = LogisticsTypeEnum.PickUp;
                }

                ProductSkuPrice skuPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,siteId);
                RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(productSku.getTenantId(), dtenantId, productSku.getId(),siteId );

                BigDecimal platformPickUpPrice = skuPrice.getPlatformPickUpPrice();
                BigDecimal platformDropShippingPrice = skuPrice.getPlatformDropShippingPrice();
                BigDecimal shippingFee = null;
                String warehouseSysCode = null;
                if(CollUtil.isNotEmpty(deliveryFeeByErpMap)){
                    // 这里直接用,上面已经算了会员逻辑了
                    ConcurrentHashMap<String, DeliveryFeeStock> hashMap = deliveryFeeByErpMap.get(productSku.getTenantId());
                    if(CollUtil.isNotEmpty(hashMap)){
                        DeliveryFeeStock deliveryFeeStock = hashMap.get(productSku.getProductSkuCode());
                        if(ObjectUtil.isNotEmpty(deliveryFeeStock)){
                            shippingFee = deliveryFeeStock.getFee();
                            warehouseSysCode = deliveryFeeStock.getWarehouseSysCode();
                        }
                    }

                }
                if (ObjectUtil.isNotEmpty(memberPrice) ) {
                    BigDecimal pickUpPrice = memberPrice.getPlatformPickUpPrice();
                    if(ObjectUtil.isNotEmpty(pickUpPrice)){

                        platformPickUpPrice = memberPrice.getPlatformPickUpPrice();

                    }
                }
                if(ObjectUtil.isNotEmpty(shippingFee)){
                    platformDropShippingPrice = platformPickUpPrice.add(shippingFee);
                }else {
                    platformDropShippingPrice = null;
                }
                String logisticsCarrier = null;
                String logisticsServiceName = null;
                String logisticsCarrierCode = null;
                if(CollUtil.isNotEmpty(productSkuPriceResultHashMap)){
                    ConcurrentHashMap<String, DeliveryFeeByErpResponse> map = productSkuPriceResultHashMap.get(productSku.getTenantId());
                    if (CollUtil.isNotEmpty(map)) {
                        DeliveryFeeByErpResponse deliveryFeeByErpResponse = map.get(productSkuCode);
                        logisticsCarrier = deliveryFeeByErpResponse.getCarrierCode();
                        logisticsServiceName = deliveryFeeByErpResponse.getLogisticCode();
                        logisticsCarrierCode = deliveryFeeByErpResponse.getCarrierCode();
                    }
                }


                //开始生成临时订单
                OrderImportTemp temp = new OrderImportTemp();
                temp.setProductSkuCode(productSkuCode);
                temp.setProductQuantity(quantity);
                temp.setRecipientName(addressVo.getFullName());
                temp.setAddress1(addressVo.getAddress1());
                temp.setAddress2(addressVo.getAddress2());
                temp.setCity(addressVo.getCity());
                temp.setStateCode(addressVo.getStateCode());
                temp.setZipCode(addressVo.getZipCode());
                temp.setPhoneNumber(addressVo.getPhoneNumber());

                temp.setLogisticsCarrier(logisticsCarrier);
                temp.setLogisticsServiceName(logisticsServiceName);
                temp.setLogisticsCarrierCode(logisticsCarrierCode);

                temp.setCountryCode(countryCode);
                temp.setSiteId(siteId);
                temp.setCurrencySymbol(siteByCountryCode.getCurrencySymbol());
                temp.setCurrencyCode(siteByCountryCode.getCurrencyCode());

                temp.setLogisticsType(logisticsType);
                String orderExtendId = orderCodeGeneratorV2.generateOrderNumber(BusinessCodeEnum.NewOrderNo);
                temp.setOrderNo(orderExtendId);
                temp.setOrderExtendId(orderExtendId);
                temp.setTempOrderNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.TempOrderNo));
                temp.setLogisticsThirdBilling(false);

                temp.setPickUpPrice(platformPickUpPrice);
                temp.setPickUpTotalAmount(NumberUtil.mul(platformPickUpPrice, quantity));
                // 加入会员价的逻辑+尾程测算
                temp.setDropShippingPrice(platformDropShippingPrice);
                temp.setDropShippingTotalAmount(platformDropShippingPrice==null?null:NumberUtil.mul(platformDropShippingPrice, quantity));
                temp.setTempState(OrderTempState.Temporary);
                // 尾程费用
                temp.setShippingFee(shippingFee);
                temp.setShippingTotalAmount(shippingFee==null?null:NumberUtil.mul(shippingFee, quantity));
                //商品信息
                String productName = product.getName();
                temp.setProductName(productName);
                temp.setProductImageShowUrl(iProductSkuAttachmentService.getPrimaryImgBySkuId(productSku.getId()));
                temp.setOrderSource(OrderSourceEnum.MALL_ORDER.getValue());
                temp.setWarehouseSystemCode(warehouseSysCode);
                DataUtil.convertNullString2Null(temp);
                tempList.add(temp);
            } else {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_OFF_SHELF.args(productSkuCode));
            }
        }

        OrderImportRecord record = new OrderImportRecord();
        record.setImportRecordNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderImportRecordNo));
        record.setImportFileName(null);
        record.setImportType(ImportTypeEnum.ShippingCart);
        record.setOrderTempState(0);
        record.setImportState(ImportStateEnum.Pending);
        iOrderImportRecordService.save(record);
        Long recordId = record.getId();
        tempList.forEach(temp -> temp.setRecordId(recordId));
        iOrderImportTempService.saveBatch(tempList);

        return R.ok(new ShippingCartOrderVo(record.getImportRecordNo()));
    }

    //    /**
//     * 功能描述：收到原始最终运费后,4位精度
//     *
//     * @param ruleCustomizerTenantId  规则定制器租户id
//     * @param ruleFollowerTenantId    规则跟随者租户id
//     * @param finalDeliveryFeeFromErp erp原始最终交付费
//     * @param isDistributionCalculate
//     * <AUTHOR>
//     * @date 2024/07/30
//     */
//    public BigDecimal getOriginalFinalDeliveryFeeAfterDiscount(String ruleCustomizerTenantId, String ruleFollowerTenantId, BigDecimal finalDeliveryFeeFromErp,
//                                                               boolean isDistributionCalculate) {
//        // 折扣系数实际要落在等级,所以要先找等级
//        LambdaQueryWrapper<MemberRuleRelation> wrapper = new LambdaQueryWrapper<MemberRuleRelation>()
//            .eq(MemberRuleRelation::getRuleCustomizerTenantId, ruleCustomizerTenantId)
//            .eq(MemberRuleRelation::getRuleFollowerTenantId, ruleFollowerTenantId)
//            .eq(MemberRuleRelation::getDelFlag, 0);
//        MemberRuleRelation one = memberRuleRelationService.getOne(wrapper);
//        BigDecimal sum;
//        // 有会员价
//        if(ObjectUtil.isNotEmpty(one)){
//            Long levelId = one.getLevelId();
//            MemberLevel memberLevel = TenantHelper.ignore(()->iMemberLevelService.getById(levelId));
//            Integer status = memberLevel.getStatus();
//            if(status==1){
//                sum = finalDeliveryFeeFromErp.multiply(BigDecimal.valueOf(1.13));
//            }else {
//                Long dictCode = memberLevel.getDictCode();
//                BigDecimal discountFactor = memberSupport.getDiscountFactor(dictCode);
//                log.info(StrUtil.format("[获取ERP尾程派送费],计算会员尾程派送费，ERP返回派送费：{}，折扣系数：{}",finalDeliveryFeeFromErp,discountFactor ));
//                if(ObjectUtil.isEmpty(discountFactor)){
//                    throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费],计算会员尾程派送费异常,未获取到会员尾程派送费折扣系数"));
//                }
//                if(isDistributionCalculate){
//                    sum = finalDeliveryFeeFromErp;
//                }else {
//                    discountFactor = discountFactor.divide(new BigDecimal("100"),2, RoundingMode.HALF_UP);
//                    sum = finalDeliveryFeeFromErp.add(finalDeliveryFeeFromErp.multiply(discountFactor)).setScale(2, RoundingMode.HALF_UP);
//                }
//
//            }
//
//        }else{
//            if(isDistributionCalculate){
//                sum = finalDeliveryFeeFromErp;
//            }else {
//                log.info(StrUtil.format("[获取ERP尾程派送费],计算尾程派送费，ERP返回派送费：{}，折扣系数：{}",finalDeliveryFeeFromErp,13 ));
//                sum = finalDeliveryFeeFromErp.multiply(BigDecimal.valueOf(1.13)).setScale(2, RoundingMode.HALF_UP);
//            }
//
//        }
//
//        return sum;
//
//    }
    public BigDecimal getOriginalFinalDeliveryFeeAfterDiscount(String ruleCustomizerTenantId, String ruleFollowerTenantId, BigDecimal finalDeliveryFeeFromErp,
                                                               boolean isDistributionCalculate) {
            return finalDeliveryFeeFromErp;
    }

    @Override
    public R shippingCartCheck(String productSkuCode, String logisticType) {
        String[] split = productSkuCode.split(",");
        for (String s : split) {
            Product product = iProductService.queryByProductSkuCode(s);
            if (ObjectUtil.isNull(product)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODCUT_NOT_FOUND_ERROR);
            }
            if (ObjectUtil.notEqual(SupportedLogisticsEnum.All.name(), product.getSupportedLogistics().name())) {
                if ((ObjectUtil.equal(SupportedLogisticsEnum.PickUpOnly.name(), product.getSupportedLogistics().name())
                    && ObjectUtil.notEqual(SupportedLogisticsEnum.PickUpOnly.name(), logisticType))) {
                    throw new RuntimeException("商品发货方式已更新成自提,请重新加购");
                }
                if ((ObjectUtil.equal(SupportedLogisticsEnum.DropShippingOnly.name(), product.getSupportedLogistics()
                                                                                             .name())
                    && ObjectUtil.notEqual(SupportedLogisticsEnum.DropShippingOnly.name(), logisticType))) {
                    throw new RuntimeException("商品发货方式已更新成代发,请重新加购");
                }
            }
        }

        return R.ok();
    }
}
